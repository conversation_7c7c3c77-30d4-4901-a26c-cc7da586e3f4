{"name": "@papra/root", "version": "0.3.0", "packageManager": "pnpm@10.12.3", "description": "Papra document management monorepo root", "author": "Corentin <PERSON> <<EMAIL>> (https://corentin.tech)", "license": "AGPL-3.0-or-later", "keywords": [], "scripts": {"docker:build:root": "docker build -t papra -f docker/Dockerfile .", "docker:build:rootless": "docker build -t papra-rootless -f docker/Dockerfile.rootless .", "version": "changeset version && pnpm install --no-frozen-lockfile", "changeset": "changeset", "build:packages": "pnpm --filter './packages/*' --stream build"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.29.3"}}