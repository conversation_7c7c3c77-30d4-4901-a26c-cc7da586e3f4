// Do not manually edit this file.
// This file is dynamically generated when the dev server runs (or using the `pnpm script:generate-i18n-types` command).
// Keys are extracted from the en.yml file.
// Source code : src/plugins/i18n-types/i18n-types.services.ts

export type LocaleKeys =
  | 'auth.request-password-reset.title'
  | 'auth.request-password-reset.description'
  | 'auth.request-password-reset.requested'
  | 'auth.request-password-reset.back-to-login'
  | 'auth.request-password-reset.form.email.label'
  | 'auth.request-password-reset.form.email.placeholder'
  | 'auth.request-password-reset.form.email.required'
  | 'auth.request-password-reset.form.email.invalid'
  | 'auth.request-password-reset.form.submit'
  | 'auth.reset-password.title'
  | 'auth.reset-password.description'
  | 'auth.reset-password.reset'
  | 'auth.reset-password.back-to-login'
  | 'auth.reset-password.form.new-password.label'
  | 'auth.reset-password.form.new-password.placeholder'
  | 'auth.reset-password.form.new-password.required'
  | 'auth.reset-password.form.new-password.min-length'
  | 'auth.reset-password.form.new-password.max-length'
  | 'auth.reset-password.form.submit'
  | 'auth.email-provider.open'
  | 'auth.login.title'
  | 'auth.login.description'
  | 'auth.login.login-with-provider'
  | 'auth.login.no-account'
  | 'auth.login.register'
  | 'auth.login.form.email.label'
  | 'auth.login.form.email.placeholder'
  | 'auth.login.form.email.required'
  | 'auth.login.form.email.invalid'
  | 'auth.login.form.password.label'
  | 'auth.login.form.password.placeholder'
  | 'auth.login.form.password.required'
  | 'auth.login.form.remember-me.label'
  | 'auth.login.form.forgot-password.label'
  | 'auth.login.form.submit'
  | 'auth.register.title'
  | 'auth.register.description'
  | 'auth.register.register-with-email'
  | 'auth.register.register-with-provider'
  | 'auth.register.providers.google'
  | 'auth.register.providers.github'
  | 'auth.register.have-account'
  | 'auth.register.login'
  | 'auth.register.registration-disabled.title'
  | 'auth.register.registration-disabled.description'
  | 'auth.register.form.email.label'
  | 'auth.register.form.email.placeholder'
  | 'auth.register.form.email.required'
  | 'auth.register.form.email.invalid'
  | 'auth.register.form.password.label'
  | 'auth.register.form.password.placeholder'
  | 'auth.register.form.password.required'
  | 'auth.register.form.password.min-length'
  | 'auth.register.form.password.max-length'
  | 'auth.register.form.name.label'
  | 'auth.register.form.name.placeholder'
  | 'auth.register.form.name.required'
  | 'auth.register.form.name.max-length'
  | 'auth.register.form.submit'
  | 'auth.email-validation-required.title'
  | 'auth.email-validation-required.description'
  | 'auth.legal-links.description'
  | 'auth.legal-links.terms'
  | 'auth.legal-links.privacy'
  | 'auth.no-auth-provider.title'
  | 'auth.no-auth-provider.description'
  | 'user.settings.title'
  | 'user.settings.description'
  | 'user.settings.email.title'
  | 'user.settings.email.description'
  | 'user.settings.email.label'
  | 'user.settings.name.title'
  | 'user.settings.name.description'
  | 'user.settings.name.label'
  | 'user.settings.name.placeholder'
  | 'user.settings.name.update'
  | 'user.settings.name.updated'
  | 'user.settings.logout.title'
  | 'user.settings.logout.description'
  | 'user.settings.logout.button'
  | 'organizations.list.title'
  | 'organizations.list.description'
  | 'organizations.list.create-new'
  | 'organizations.details.no-documents.title'
  | 'organizations.details.no-documents.description'
  | 'organizations.details.upload-documents'
  | 'organizations.details.documents-count'
  | 'organizations.details.total-size'
  | 'organizations.details.latest-documents'
  | 'organizations.create.title'
  | 'organizations.create.description'
  | 'organizations.create.back'
  | 'organizations.create.error.max-count-reached'
  | 'organizations.create.form.name.label'
  | 'organizations.create.form.name.placeholder'
  | 'organizations.create.form.name.required'
  | 'organizations.create.form.submit'
  | 'organizations.create.success'
  | 'organizations.create-first.title'
  | 'organizations.create-first.description'
  | 'organizations.create-first.default-name'
  | 'organizations.create-first.user-name'
  | 'organization.settings.title'
  | 'organization.settings.page.title'
  | 'organization.settings.page.description'
  | 'organization.settings.name.title'
  | 'organization.settings.name.update'
  | 'organization.settings.name.placeholder'
  | 'organization.settings.name.updated'
  | 'organization.settings.subscription.title'
  | 'organization.settings.subscription.description'
  | 'organization.settings.subscription.manage'
  | 'organization.settings.subscription.error'
  | 'organization.settings.delete.title'
  | 'organization.settings.delete.description'
  | 'organization.settings.delete.confirm.title'
  | 'organization.settings.delete.confirm.message'
  | 'organization.settings.delete.confirm.confirm-button'
  | 'organization.settings.delete.confirm.cancel-button'
  | 'organization.settings.delete.success'
  | 'organizations.members.title'
  | 'organizations.members.description'
  | 'organizations.members.invite-member'
  | 'organizations.members.invite-member-disabled-tooltip'
  | 'organizations.members.remove-from-organization'
  | 'organizations.members.role'
  | 'organizations.members.roles.owner'
  | 'organizations.members.roles.admin'
  | 'organizations.members.roles.member'
  | 'organizations.members.delete.confirm.title'
  | 'organizations.members.delete.confirm.message'
  | 'organizations.members.delete.confirm.confirm-button'
  | 'organizations.members.delete.confirm.cancel-button'
  | 'organizations.members.delete.success'
  | 'organizations.members.update-role.success'
  | 'organizations.members.table.headers.name'
  | 'organizations.members.table.headers.email'
  | 'organizations.members.table.headers.role'
  | 'organizations.members.table.headers.created'
  | 'organizations.members.table.headers.actions'
  | 'organizations.invite-member.title'
  | 'organizations.invite-member.description'
  | 'organizations.invite-member.form.email.label'
  | 'organizations.invite-member.form.email.placeholder'
  | 'organizations.invite-member.form.email.required'
  | 'organizations.invite-member.form.role.label'
  | 'organizations.invite-member.form.submit'
  | 'organizations.invite-member.success.message'
  | 'organizations.invite-member.success.description'
  | 'organizations.invite-member.error.message'
  | 'organizations.invitations.title'
  | 'organizations.invitations.description'
  | 'organizations.invitations.list.cta'
  | 'organizations.invitations.list.empty.title'
  | 'organizations.invitations.list.empty.description'
  | 'organizations.invitations.status.pending'
  | 'organizations.invitations.status.accepted'
  | 'organizations.invitations.status.rejected'
  | 'organizations.invitations.status.expired'
  | 'organizations.invitations.status.cancelled'
  | 'organizations.invitations.resend'
  | 'organizations.invitations.cancel.title'
  | 'organizations.invitations.cancel.description'
  | 'organizations.invitations.cancel.confirm'
  | 'organizations.invitations.cancel.cancel'
  | 'organizations.invitations.resend.title'
  | 'organizations.invitations.resend.description'
  | 'organizations.invitations.resend.confirm'
  | 'organizations.invitations.resend.cancel'
  | 'invitations.list.title'
  | 'invitations.list.description'
  | 'invitations.list.empty.title'
  | 'invitations.list.empty.description'
  | 'invitations.list.headers.organization'
  | 'invitations.list.headers.status'
  | 'invitations.list.headers.created'
  | 'invitations.list.headers.actions'
  | 'invitations.list.actions.accept'
  | 'invitations.list.actions.reject'
  | 'invitations.list.actions.accept.success.message'
  | 'invitations.list.actions.accept.success.description'
  | 'invitations.list.actions.reject.success.message'
  | 'invitations.list.actions.reject.success.description'
  | 'documents.list.title'
  | 'documents.list.no-documents.title'
  | 'documents.list.no-documents.description'
  | 'documents.list.no-results'
  | 'documents.tabs.info'
  | 'documents.tabs.content'
  | 'documents.tabs.activity'
  | 'documents.deleted.message'
  | 'documents.actions.download'
  | 'documents.actions.open-in-new-tab'
  | 'documents.actions.restore'
  | 'documents.actions.delete'
  | 'documents.actions.edit'
  | 'documents.actions.cancel'
  | 'documents.actions.save'
  | 'documents.actions.saving'
  | 'documents.content.alert'
  | 'documents.info.id'
  | 'documents.info.name'
  | 'documents.info.type'
  | 'documents.info.size'
  | 'documents.info.created-at'
  | 'documents.info.updated-at'
  | 'documents.info.never'
  | 'documents.rename.title'
  | 'documents.rename.form.name.label'
  | 'documents.rename.form.name.placeholder'
  | 'documents.rename.form.name.required'
  | 'documents.rename.form.name.max-length'
  | 'documents.rename.form.submit'
  | 'documents.rename.success'
  | 'documents.rename.cancel'
  | 'import-documents.title.error'
  | 'import-documents.title.success'
  | 'import-documents.title.pending'
  | 'import-documents.title.none'
  | 'import-documents.no-import-in-progress'
  | 'documents.deleted.title'
  | 'documents.deleted.empty.title'
  | 'documents.deleted.empty.description'
  | 'documents.deleted.retention-notice'
  | 'documents.deleted.deleted-at'
  | 'documents.deleted.restoring'
  | 'documents.deleted.deleting'
  | 'documents.preview.unknown-file-type'
  | 'documents.preview.binary-file'
  | 'trash.delete-all.button'
  | 'trash.delete-all.confirm.title'
  | 'trash.delete-all.confirm.description'
  | 'trash.delete-all.confirm.label'
  | 'trash.delete-all.confirm.cancel'
  | 'trash.delete.button'
  | 'trash.delete.confirm.title'
  | 'trash.delete.confirm.description'
  | 'trash.delete.confirm.label'
  | 'trash.delete.confirm.cancel'
  | 'trash.deleted.success.title'
  | 'trash.deleted.success.description'
  | 'activity.document.created'
  | 'activity.document.updated.single'
  | 'activity.document.updated.multiple'
  | 'activity.document.updated'
  | 'activity.document.deleted'
  | 'activity.document.restored'
  | 'activity.document.tagged'
  | 'activity.document.untagged'
  | 'activity.document.user.name'
  | 'activity.load-more'
  | 'activity.no-more-activities'
  | 'tags.no-tags.title'
  | 'tags.no-tags.description'
  | 'tags.no-tags.create-tag'
  | 'tags.title'
  | 'tags.description'
  | 'tags.create'
  | 'tags.update'
  | 'tags.delete'
  | 'tags.delete.confirm.title'
  | 'tags.delete.confirm.message'
  | 'tags.delete.confirm.confirm-button'
  | 'tags.delete.confirm.cancel-button'
  | 'tags.delete.success'
  | 'tags.create.success'
  | 'tags.update.success'
  | 'tags.form.name.label'
  | 'tags.form.name.placeholder'
  | 'tags.form.name.required'
  | 'tags.form.name.max-length'
  | 'tags.form.color.label'
  | 'tags.form.color.required'
  | 'tags.form.color.invalid'
  | 'tags.form.description.label'
  | 'tags.form.description.optional'
  | 'tags.form.description.placeholder'
  | 'tags.form.description.max-length'
  | 'tags.form.no-description'
  | 'tags.table.headers.tag'
  | 'tags.table.headers.description'
  | 'tags.table.headers.documents'
  | 'tags.table.headers.created'
  | 'tags.table.headers.actions'
  | 'tagging-rules.field.name'
  | 'tagging-rules.field.content'
  | 'tagging-rules.operator.equals'
  | 'tagging-rules.operator.not-equals'
  | 'tagging-rules.operator.contains'
  | 'tagging-rules.operator.not-contains'
  | 'tagging-rules.operator.starts-with'
  | 'tagging-rules.operator.ends-with'
  | 'tagging-rules.list.title'
  | 'tagging-rules.list.description'
  | 'tagging-rules.list.demo-warning'
  | 'tagging-rules.list.no-tagging-rules.title'
  | 'tagging-rules.list.no-tagging-rules.description'
  | 'tagging-rules.list.no-tagging-rules.create-tagging-rule'
  | 'tagging-rules.list.card.no-conditions'
  | 'tagging-rules.list.card.one-condition'
  | 'tagging-rules.list.card.conditions'
  | 'tagging-rules.list.card.delete'
  | 'tagging-rules.list.card.edit'
  | 'tagging-rules.create.title'
  | 'tagging-rules.create.success'
  | 'tagging-rules.create.error'
  | 'tagging-rules.create.submit'
  | 'tagging-rules.form.name.label'
  | 'tagging-rules.form.name.placeholder'
  | 'tagging-rules.form.name.min-length'
  | 'tagging-rules.form.name.max-length'
  | 'tagging-rules.form.description.label'
  | 'tagging-rules.form.description.placeholder'
  | 'tagging-rules.form.description.max-length'
  | 'tagging-rules.form.conditions.label'
  | 'tagging-rules.form.conditions.description'
  | 'tagging-rules.form.conditions.add-condition'
  | 'tagging-rules.form.conditions.no-conditions.title'
  | 'tagging-rules.form.conditions.no-conditions.description'
  | 'tagging-rules.form.conditions.no-conditions.confirm'
  | 'tagging-rules.form.conditions.no-conditions.cancel'
  | 'tagging-rules.form.conditions.value.placeholder'
  | 'tagging-rules.form.conditions.value.min-length'
  | 'tagging-rules.form.tags.label'
  | 'tagging-rules.form.tags.description'
  | 'tagging-rules.form.tags.min-length'
  | 'tagging-rules.form.tags.add-tag'
  | 'tagging-rules.form.submit'
  | 'tagging-rules.update.title'
  | 'tagging-rules.update.error'
  | 'tagging-rules.update.submit'
  | 'tagging-rules.update.cancel'
  | 'intake-emails.title'
  | 'intake-emails.description'
  | 'intake-emails.disabled.title'
  | 'intake-emails.disabled.description'
  | 'intake-emails.disabled.documentation'
  | 'intake-emails.info'
  | 'intake-emails.empty.title'
  | 'intake-emails.empty.description'
  | 'intake-emails.empty.generate'
  | 'intake-emails.count'
  | 'intake-emails.new'
  | 'intake-emails.disabled-label'
  | 'intake-emails.no-origins'
  | 'intake-emails.allowed-origins'
  | 'intake-emails.actions.enable'
  | 'intake-emails.actions.disable'
  | 'intake-emails.actions.manage-origins'
  | 'intake-emails.actions.delete'
  | 'intake-emails.delete.confirm.title'
  | 'intake-emails.delete.confirm.message'
  | 'intake-emails.delete.confirm.confirm-button'
  | 'intake-emails.delete.confirm.cancel-button'
  | 'intake-emails.delete.success'
  | 'intake-emails.create.success'
  | 'intake-emails.update.success.enabled'
  | 'intake-emails.update.success.disabled'
  | 'intake-emails.allowed-origins.title'
  | 'intake-emails.allowed-origins.description'
  | 'intake-emails.allowed-origins.add.label'
  | 'intake-emails.allowed-origins.add.placeholder'
  | 'intake-emails.allowed-origins.add.button'
  | 'intake-emails.allowed-origins.add.error.exists'
  | 'api-keys.permissions.documents.title'
  | 'api-keys.permissions.documents.documents:create'
  | 'api-keys.permissions.documents.documents:read'
  | 'api-keys.permissions.documents.documents:update'
  | 'api-keys.permissions.documents.documents:delete'
  | 'api-keys.permissions.tags.title'
  | 'api-keys.permissions.tags.tags:create'
  | 'api-keys.permissions.tags.tags:read'
  | 'api-keys.permissions.tags.tags:update'
  | 'api-keys.permissions.tags.tags:delete'
  | 'api-keys.create.title'
  | 'api-keys.create.description'
  | 'api-keys.create.success'
  | 'api-keys.create.back'
  | 'api-keys.create.form.name.label'
  | 'api-keys.create.form.name.placeholder'
  | 'api-keys.create.form.name.required'
  | 'api-keys.create.form.permissions.label'
  | 'api-keys.create.form.permissions.required'
  | 'api-keys.create.form.submit'
  | 'api-keys.create.created.title'
  | 'api-keys.create.created.description'
  | 'api-keys.list.title'
  | 'api-keys.list.description'
  | 'api-keys.list.create'
  | 'api-keys.list.empty.title'
  | 'api-keys.list.empty.description'
  | 'api-keys.list.card.last-used'
  | 'api-keys.list.card.never'
  | 'api-keys.list.card.created'
  | 'api-keys.delete.success'
  | 'api-keys.delete.confirm.title'
  | 'api-keys.delete.confirm.message'
  | 'api-keys.delete.confirm.confirm-button'
  | 'api-keys.delete.confirm.cancel-button'
  | 'webhooks.list.title'
  | 'webhooks.list.description'
  | 'webhooks.list.empty.title'
  | 'webhooks.list.empty.description'
  | 'webhooks.list.create'
  | 'webhooks.list.card.last-triggered'
  | 'webhooks.list.card.never'
  | 'webhooks.list.card.created'
  | 'webhooks.create.title'
  | 'webhooks.create.description'
  | 'webhooks.create.success'
  | 'webhooks.create.back'
  | 'webhooks.create.form.submit'
  | 'webhooks.create.form.name.label'
  | 'webhooks.create.form.name.placeholder'
  | 'webhooks.create.form.name.required'
  | 'webhooks.create.form.url.label'
  | 'webhooks.create.form.url.placeholder'
  | 'webhooks.create.form.url.required'
  | 'webhooks.create.form.url.invalid'
  | 'webhooks.create.form.secret.label'
  | 'webhooks.create.form.secret.placeholder'
  | 'webhooks.create.form.events.label'
  | 'webhooks.create.form.events.required'
  | 'webhooks.update.title'
  | 'webhooks.update.description'
  | 'webhooks.update.success'
  | 'webhooks.update.submit'
  | 'webhooks.update.cancel'
  | 'webhooks.update.form.secret.placeholder'
  | 'webhooks.update.form.secret.placeholder-redacted'
  | 'webhooks.update.form.rotate-secret.button'
  | 'webhooks.delete.success'
  | 'webhooks.delete.confirm.title'
  | 'webhooks.delete.confirm.message'
  | 'webhooks.delete.confirm.confirm-button'
  | 'webhooks.delete.confirm.cancel-button'
  | 'webhooks.events.documents.document:created.description'
  | 'webhooks.events.documents.document:deleted.description'
  | 'layout.menu.home'
  | 'layout.menu.documents'
  | 'layout.menu.tags'
  | 'layout.menu.tagging-rules'
  | 'layout.menu.deleted-documents'
  | 'layout.menu.organization-settings'
  | 'layout.menu.api-keys'
  | 'layout.menu.settings'
  | 'layout.menu.account'
  | 'layout.menu.general-settings'
  | 'layout.menu.intake-emails'
  | 'layout.menu.webhooks'
  | 'layout.menu.members'
  | 'layout.menu.invitations'
  | 'layout.theme.light'
  | 'layout.theme.dark'
  | 'layout.theme.system'
  | 'layout.search.placeholder'
  | 'layout.menu.import-document'
  | 'user-menu.account-settings'
  | 'user-menu.api-keys'
  | 'user-menu.invitations'
  | 'user-menu.language'
  | 'user-menu.logout'
  | 'command-palette.search.placeholder'
  | 'command-palette.no-results'
  | 'command-palette.sections.documents'
  | 'command-palette.sections.theme'
  | 'api-errors.document.already_exists'
  | 'api-errors.document.file_too_big'
  | 'api-errors.intake_email.limit_reached'
  | 'api-errors.user.max_organization_count_reached'
  | 'api-errors.default'
  | 'api-errors.organization.invitation_already_exists'
  | 'api-errors.user.already_in_organization'
  | 'api-errors.user.organization_invitation_limit_reached'
  | 'api-errors.demo.not_available'
  | 'api-errors.tags.already_exists'
  | 'not-found.title'
  | 'not-found.description'
  | 'not-found.back-to-home'
  | 'demo.popup.description'
  | 'demo.popup.discord'
  | 'demo.popup.discord-link-label'
  | 'demo.popup.reset'
  | 'demo.popup.hide'
  | 'color-picker.hue'
  | 'color-picker.saturation'
  | 'color-picker.lightness'
  | 'color-picker.select-color'
  | 'color-picker.select-a-color';
