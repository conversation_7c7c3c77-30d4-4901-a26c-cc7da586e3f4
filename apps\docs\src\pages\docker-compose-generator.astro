---
import StarlightPage from '@astrojs/starlight/components/StarlightPage.astro';
import DockerComposeGeneratorComp from '../docker-compose-generator/dc-generator.astro';
---

<StarlightPage
  frontmatter={{
        title: '<PERSON><PERSON> docker-compose.yml generator',
        description: 'Generate a custom docker-compose.yml file for Papra, tailored to your needs.',
        tableOfContents: false,
    }}
>
    <p>This tool will help you generate a custom docker-compose.yml file for Papra, tailored to your needs. You can personalize the service name, the port, the auth secret, and the source image.</p>
    <p>For more configuration options, you can use the <a href="/self-hosting/configuration">configuration reference</a>.</p>
    <DockerComposeGeneratorComp />
</StarlightPage>
