{"name": "@papra/app-server", "type": "module", "version": "0.6.3", "private": true, "packageManager": "pnpm@10.12.3", "description": "Papra app server", "author": "Corentin <PERSON> <<EMAIL>> (https://corentin.tech)", "license": "AGPL-3.0-or-later", "keywords": [], "scripts": {"dev": "tsx watch --env-file-if-exists=.env  src/index.ts | crowlog-pretty", "build": "pnpm esbuild --bundle src/index.ts --platform=node --packages=external --format=esm --outfile=dist/index.js --minify", "start": "node dist/index.js", "start:with-migrations": "pnpm migrate:up && pnpm start", "lint": "eslint .", "lint:fix": "eslint --fix .", "test": "vitest run", "test:watch": "vitest watch", "typecheck": "tsc --noEmit", "migrate:up": "tsx --env-file-if-exists=.env src/scripts/migrate-up.script.ts", "migrate:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "clean:dist": "rm -rf dist", "clean:db": "rm db.sqlite", "clean:storage": "rm -rf local-documents", "clean:all": "pnpm clean:db && pnpm clean:storage", "dev:reset": "pnpm clean:all && pnpm migrate:up", "script:send-intake-email": "tsx --env-file-if-exists=.env src/scripts/send-intake-email.script.ts | crowlog-pretty", "stripe:webhook": "stripe listen --forward-to localhost:1221/api/stripe/webhook"}, "dependencies": {"@aws-sdk/client-s3": "^3.835.0", "@aws-sdk/lib-storage": "^3.835.0", "@azure/storage-blob": "^12.27.0", "@corentinth/chisels": "^1.3.1", "@corentinth/friendly-ids": "^0.0.1", "@crowlog/async-context-plugin": "^1.2.1", "@crowlog/logger": "^1.2.1", "@hono/node-server": "^1.14.4", "@libsql/client": "^0.14.0", "@owlrelay/api-sdk": "^0.0.2", "@owlrelay/webhook": "^0.0.3", "@papra/lecture": "workspace:*", "@papra/webhooks": "workspace:*", "@paralleldrive/cuid2": "^2.2.2", "backblaze-b2": "^1.7.1", "better-auth": "catalog:", "c12": "^3.0.4", "chokidar": "^4.0.3", "date-fns": "^4.1.0", "drizzle-kit": "^0.30.6", "drizzle-orm": "^0.38.4", "figue": "^2.2.3", "hono": "^4.8.2", "lodash-es": "^4.17.21", "mime-types": "^3.0.1", "nanoid": "^5.1.5", "node-cron": "^3.0.3", "nodemailer": "^7.0.3", "p-limit": "^6.2.0", "p-queue": "^8.1.0", "picomatch": "^4.0.2", "posthog-node": "^4.18.0", "resend": "^4.6.0", "sanitize-html": "^2.17.0", "stripe": "^17.7.0", "tsx": "^4.20.3", "zod": "^3.25.67"}, "devDependencies": {"@antfu/eslint-config": "catalog:", "@crowlog/pretty": "^1.2.1", "@total-typescript/ts-reset": "^0.6.1", "@types/backblaze-b2": "^1.5.6", "@types/lodash-es": "^4.17.12", "@types/mime-types": "^2.1.4", "@types/node": "catalog:", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/picomatch": "^4.0.0", "@types/sanitize-html": "^2.16.0", "@vitest/coverage-v8": "catalog:", "esbuild": "^0.24.2", "eslint": "catalog:", "memfs": "^4.17.2", "typescript": "catalog:", "vitest": "catalog:"}}