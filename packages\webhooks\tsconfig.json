{"compilerOptions": {"target": "ESNext", "moduleDetection": "force", "module": "preserve", "resolveJsonModule": true, "allowJs": true, "strict": true, "noImplicitOverride": true, "noUncheckedIndexedAccess": true, "noEmit": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "verbatimModuleSyntax": true, "skipLibCheck": true}}