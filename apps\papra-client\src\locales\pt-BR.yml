# Authentication

auth.request-password-reset.title: Redefina sua senha
auth.request-password-reset.description: Insira seu e-mail para redefinir sua senha.
auth.request-password-reset.requested: Se uma conta com este e-mail existe, enviamos uma mensagem para redefinir sua senha.
auth.request-password-reset.back-to-login: Voltar para o login
auth.request-password-reset.form.email.label: E-mail
auth.request-password-reset.form.email.placeholder: 'Exemplo: <EMAIL>'
auth.request-password-reset.form.email.required: Por favor, insira seu endereço de e-mail
auth.request-password-reset.form.email.invalid: Este endereço de e-mail é inválido
auth.request-password-reset.form.submit: Solicitar redefinição de senha

auth.reset-password.title: Redefina sua senha
auth.reset-password.description: Insira sua nova senha para redefinir sua senha.
auth.reset-password.reset: Sua senha foi redefinida.
auth.reset-password.back-to-login: Voltar para o login
auth.reset-password.form.new-password.label: Nova senha
auth.reset-password.form.new-password.placeholder: 'Exemplo: **********'
auth.reset-password.form.new-password.required: Por favor, insira sua nova senha
auth.reset-password.form.new-password.min-length: A senha deve ter pelo menos {{ minLength }} caracteres
auth.reset-password.form.new-password.max-length: A senha deve ter menos de {{ maxLength }} caracteres
auth.reset-password.form.submit: Redefinir senha

auth.email-provider.open: Abrir {{ provider }}

auth.login.title: Acessar o Papra
auth.login.description: Insira seu e-mail ou use um login de rede social para acessar sua conta no Papra.
auth.login.login-with-provider: Entrar com {{ provider }}
auth.login.no-account: Não tem uma conta?
auth.login.register: Cadastre-se
auth.login.form.email.label: E-mail
auth.login.form.email.placeholder: 'Exemplo: <EMAIL>'
auth.login.form.email.required: Por favor, insira seu endereço de e-mail
auth.login.form.email.invalid: Este endereço de e-mail é inválido
auth.login.form.password.label: Senha
auth.login.form.password.placeholder: Defina uma senha
auth.login.form.password.required: Por favor, insira sua senha
auth.login.form.remember-me.label: Lembrar de mim
auth.login.form.forgot-password.label: Esqueceu a senha?
auth.login.form.submit: Entrar

auth.register.title: Cadastre-se no Papra
auth.register.description: Crie uma conta para começar a usar o Papra.
auth.register.register-with-email: Cadastrar com e-mail
auth.register.register-with-provider: Cadastrar com {{ provider }}
auth.register.providers.google: Google
auth.register.providers.github: GitHub
auth.register.have-account: Já tem uma conta?
auth.register.login: Entrar
auth.register.registration-disabled.title: Cadastro desativado
auth.register.registration-disabled.description: A criação de novas contas está desativada nesta instância do Papra. Somente usuários com contas existentes podem acessar. Se você acha que isso é um engano, entre em contato com o administrador desta instância.
auth.register.form.email.label: E-mail
auth.register.form.email.placeholder: 'Exemplo: <EMAIL>'
auth.register.form.email.required: Por favor, insira seu endereço de e-mail
auth.register.form.email.invalid: Este endereço de e-mail é inválido
auth.register.form.password.label: Senha
auth.register.form.password.placeholder: Defina uma senha
auth.register.form.password.required: Por favor, insira sua senha
auth.register.form.password.min-length: A senha deve ter pelo menos {{ minLength }} caracteres
auth.register.form.password.max-length: A senha deve ter menos de {{ maxLength }} caracteres
auth.register.form.name.label: Nome
auth.register.form.name.placeholder: 'Exemplo: César Lattes'
auth.register.form.name.required: Por favor, insira seu nome
auth.register.form.name.max-length: O nome deve ter menos de {{ maxLength }} caracteres
auth.register.form.submit: Cadastrar

auth.email-validation-required.title: Verifique seu e-mail
auth.email-validation-required.description: Um e-mail de verificação foi enviado para seu endereço. Por favor, verifique seu e-mail clicando no link enviado.

auth.legal-links.description: Ao continuar, você reconhece que leu e concorda com os {{ terms }} e a {{ privacy }}.
auth.legal-links.terms: Termos de Serviço
auth.legal-links.privacy: Política de Privacidade

auth.no-auth-provider.title: Nenhum provedor de autenticação
auth.no-auth-provider.description: Não há provedores de autenticação habilitados nesta instância do Papra. Por favor, entre em contato com o administrador desta instância para habilitá-los.

# User settings

user.settings.title: Configurações do usuário
user.settings.description: Gerencie as configurações da sua conta aqui.

user.settings.email.title: Endereço de e-mail
user.settings.email.description: Seu endereço de e-mail não pode ser alterado.
user.settings.email.label: Endereço de e-mail

user.settings.name.title: Nome completo
user.settings.name.description: Seu nome completo é exibido para outros membros da organização.
user.settings.name.label: Nome completo
user.settings.name.placeholder: 'Ex: João da Silva'
user.settings.name.update: Atualizar nome
user.settings.name.updated: Seu nome completo foi atualizado

user.settings.logout.title: Sair
user.settings.logout.description: Encerre a sessão da sua conta. Você poderá acessá-la novamente mais tarde.
user.settings.logout.button: Sair

# Organizations

organizations.list.title: Suas organizações
organizations.list.description: Organizações são uma forma de agrupar seus documentos e gerenciar o acesso a eles. Você pode criar várias organizações e convidar membros da sua equipe para colaborar.
organizations.list.create-new: Criar nova organização

organizations.details.no-documents.title: Nenhum documento
organizations.details.no-documents.description: Ainda não há documentos nesta organização. Comece enviando documentos.
organizations.details.upload-documents: Enviar documentos
organizations.details.documents-count: documentos no total
organizations.details.total-size: tamanho total
organizations.details.latest-documents: Documentos importados recentemente

organizations.create.title: Criar uma nova organização
organizations.create.description: Seus documentos serão agrupados por organização. Você pode criar várias organizações para separar, por exemplo, documentos pessoais e profissionais.
organizations.create.back: Voltar
organizations.create.error.max-count-reached: Você atingiu o número máximo de organizações que pode criar. Se precisar criar mais, entre em contato com o suporte.
organizations.create.form.name.label: Nome da organização
organizations.create.form.name.placeholder: 'Ex: Empresa Ltda.'
organizations.create.form.name.required: Por favor, insira um nome para a organização
organizations.create.form.submit: Criar organização
organizations.create.success: Organização criada com sucesso

organizations.create-first.title: Crie sua organização
organizations.create-first.description: Seus documentos serão agrupados por organização. Você pode criar várias organizações para separar, por exemplo, documentos pessoais e profissionais.
organizations.create-first.default-name: Minha organização
organizations.create-first.user-name: Organização de {{ name }}

organization.settings.title: Configurações da Organização
organization.settings.page.title: Configurações da organização
organization.settings.page.description: Gerencie aqui as configurações da sua organização.
organization.settings.name.title: Nome da organização
organization.settings.name.update: Atualizar nome
organization.settings.name.placeholder: 'Ex: Empresa Ltda.'
organization.settings.name.updated: Nome da organização atualizado
organization.settings.subscription.title: Assinatura
organization.settings.subscription.description: Gerencie sua cobrança, faturas e formas de pagamento.
organization.settings.subscription.manage: Gerenciar assinatura
organization.settings.subscription.error: Falha ao obter o link do portal do cliente
organization.settings.delete.title: Excluir organização
organization.settings.delete.description: A exclusão desta organização removerá permanentemente todos seus dados associados.
organization.settings.delete.confirm.title: Excluir organização
organization.settings.delete.confirm.message: Tem certeza de que deseja excluir esta organização? Esta ação não pode ser desfeita e todos os dados associados serão permanentemente removidos.
organization.settings.delete.confirm.confirm-button: Excluir organização
organization.settings.delete.confirm.cancel-button: Cancelar
organization.settings.delete.success: Organização excluída

organizations.members.title: Membros
organizations.members.description: Gerencie os membros da sua organização
organizations.members.invite-member: Convidar membro
organizations.members.invite-member-disabled-tooltip: Apenas administradores ou proprietários podem convidar membros para a organização
organizations.members.remove-from-organization: Remover da organização
organizations.members.role: Função
organizations.members.roles.owner: Proprietário
organizations.members.roles.admin: Administrador
organizations.members.roles.member: Membro
organizations.members.delete.confirm.title: Remover membro
organizations.members.delete.confirm.message: Tem certeza de que deseja remover este membro da organização?
organizations.members.delete.confirm.confirm-button: Remover
organizations.members.delete.confirm.cancel-button: Cancelar
organizations.members.delete.success: Membro removido da organização
organizations.members.update-role.success: Função do membro atualizada
organizations.members.table.headers.name: Nome
organizations.members.table.headers.email: E-mail
organizations.members.table.headers.role: Função
organizations.members.table.headers.created: Criado em
organizations.members.table.headers.actions: Ações

organizations.invite-member.title: Convidar membro
organizations.invite-member.description: Convide um membro para a sua organização
organizations.invite-member.form.email.label: E-mail
organizations.invite-member.form.email.placeholder: 'Exemplo: <EMAIL>'
organizations.invite-member.form.email.required: Por favor, insira um endereço de e-mail válido
organizations.invite-member.form.role.label: Função
organizations.invite-member.form.submit: Convidar para a organização
organizations.invite-member.success.message: Membro convidado
organizations.invite-member.success.description: O e-mail foi convidado para a organização.
organizations.invite-member.error.message: Falha ao convidar o membro

organizations.invitations.title: Convites
organizations.invitations.description: Gerencie os convites da sua organização
organizations.invitations.list.cta: Convidar membro
organizations.invitations.list.empty.title: Nenhum convite pendente
organizations.invitations.list.empty.description: Você ainda não foi convidado para nenhuma organização.
organizations.invitations.status.pending: Pendente
organizations.invitations.status.accepted: Aceito
organizations.invitations.status.rejected: Rejeitado
organizations.invitations.status.expired: Expirado
organizations.invitations.status.cancelled: Cancelado
organizations.invitations.resend: Reenviar convite
organizations.invitations.cancel.title: Cancelar convite
organizations.invitations.cancel.description: Tem certeza de que deseja cancelar este convite?
organizations.invitations.cancel.confirm: Cancelar convite
organizations.invitations.cancel.cancel: Cancelar
organizations.invitations.resend.title: Reenviar convite
organizations.invitations.resend.description: Tem certeza de que deseja reenviar este convite? Um novo e-mail será enviado ao destinatário.
organizations.invitations.resend.confirm: Reenviar convite
organizations.invitations.resend.cancel: Cancelar

invitations.list.title: Convites
invitations.list.description: Gerencie os convites da sua organização
invitations.list.empty.title: Nenhum convite pendente
invitations.list.empty.description: Você ainda não foi convidado para nenhuma organização.
invitations.list.headers.organization: Organização
invitations.list.headers.status: Status
invitations.list.headers.created: Criado em
invitations.list.headers.actions: Ações
invitations.list.actions.accept: Aceitar
invitations.list.actions.reject: Rejeitar
invitations.list.actions.accept.success.message: Convite aceito
invitations.list.actions.accept.success.description: O convite foi aceito.
invitations.list.actions.reject.success.message: Convite rejeitado
invitations.list.actions.reject.success.description: O convite foi rejeitado.

# Documents

documents.list.title: Documentos
documents.list.no-documents.title: Nenhum documento
documents.list.no-documents.description: Ainda não há documentos nesta organização. Comece enviando documentos.
documents.list.no-results: Nenhum documento encontrado

documents.tabs.info: Informações
documents.tabs.content: Conteúdo
documents.tabs.activity: Atividades
documents.deleted.message: Este documento foi excluído e será deletado permanentemente em {{ days }} dias.
documents.actions.download: Baixar
documents.actions.open-in-new-tab: Abrir em nova aba
documents.actions.restore: Restaurar
documents.actions.delete: Excluir
documents.actions.edit: Editar
documents.actions.cancel: Cancelar
documents.actions.save: Salvar
documents.actions.saving: Salvando...
documents.content.alert: O conteúdo do documento é extraído automaticamente durante o envio e será utilizado apenas para fins de busca e indexação.
documents.info.id: ID
documents.info.name: Nome
documents.info.type: Tipo
documents.info.size: Tamanho
documents.info.created-at: Criado em
documents.info.updated-at: Atualizado em
documents.info.never: Nunca

documents.rename.title: Renomear documento
documents.rename.form.name.label: Nome
documents.rename.form.name.placeholder: 'Exemplo: Fatura 2024'
documents.rename.form.name.required: Por favor, insira um nome para o documento
documents.rename.form.name.max-length: O nome deve ter menos de 255 caracteres
documents.rename.form.submit: Renomear documento
documents.rename.success: Documento renomeado com sucesso
documents.rename.cancel: Cancelar

import-documents.title.error: '{{ count }} documentos falharam'
import-documents.title.success: '{{ count }} documentos importados'
import-documents.title.pending: '{{ count }} / {{ total }} documentos importados'
import-documents.title.none: Importar documentos
import-documents.no-import-in-progress: Nenhuma importação de documentos em andamento

documents.deleted.title: Documentos excluídos
documents.deleted.empty.title: Nenhum documento excluído
documents.deleted.empty.description: Você não tem documentos excluídos. Documentos excluídos serão movidos para a lixeira por {{ days }} dias.
documents.deleted.retention-notice: Todos os documentos excluídos são armazenados na lixeira por {{ days }} dias. Após esse período, os documentos serão excluídos permanentemente e não será possível restaurá-los.
documents.deleted.deleted-at: Excluído em
documents.deleted.restoring: Restaurando...
documents.deleted.deleting: Excluindo...

documents.preview.unknown-file-type: Pré-visualização não disponível para este tipo de arquivo
documents.preview.binary-file: Arquivos binários não podem ser exibidos como texto

trash.delete-all.button: Excluir tudo
trash.delete-all.confirm.title: Excluir todos os documentos permanentemente?
trash.delete-all.confirm.description: Tem certeza de que deseja excluir permanentemente todos os documentos da lixeira? Esta ação não poderá ser desfeita.
trash.delete-all.confirm.label: Excluir
trash.delete-all.confirm.cancel: Cancelar
trash.delete.button: Excluir
trash.delete.confirm.title: Excluir documento permanentemente?
trash.delete.confirm.description: Tem certeza de que deseja excluir permanentemente este documento da lixeira? Esta ação não poderá ser desfeita.
trash.delete.confirm.label: Excluir
trash.delete.confirm.cancel: Cancelar
trash.deleted.success.title: Documento excluído
trash.deleted.success.description: O documento foi excluído permanentemente.

activity.document.created: O documento foi criado
activity.document.updated.single: O {{ field }} foi atualizado
activity.document.updated.multiple: Os {{ fields }} foram atualizados
activity.document.updated: O documento foi atualizado
activity.document.deleted: O documento foi excluído
activity.document.restored: O documento foi restaurado
activity.document.tagged: A tag {{ tag }} foi adicionada
activity.document.untagged: A tag {{ tag }} foi removida

activity.document.user.name: por {{ name }}

activity.load-more: Carregar mais
activity.no-more-activities: Não há mais atividades para este documento

# Tags

tags.no-tags.title: Nenhuma tag
tags.no-tags.description: Esta organização ainda não possui tags. As tags são usadas para categorizar documentos. Você pode adicioná-las aos seus documentos para facilitar a busca e a organização.
tags.no-tags.create-tag: Criar tag

tags.title: Tags de documentos
tags.description: As tags são usadas para categorizar documentos. Você pode adicioná-las aos seus documentos para facilitar a busca e a organização.
tags.create: Criar tag
tags.update: Atualizar tag
tags.delete: Excluir tag
tags.delete.confirm.title: Excluir tag
tags.delete.confirm.message: Tem certeza de que deseja excluir esta tag? A exclusão de uma tag a removerá de todos os documentos.
tags.delete.confirm.confirm-button: Excluir
tags.delete.confirm.cancel-button: Cancelar
tags.delete.success: Tag excluída com sucesso
tags.create.success: Tag "{{ name }}" criada com sucesso.
tags.update.success: Tag "{{ name }}" atualizada com sucesso.
tags.form.name.label: Nome
tags.form.name.placeholder: 'Ex: Contratos'
tags.form.name.required: Por favor, insira um nome para a tag
tags.form.name.max-length: O nome da tag deve ter menos de 64 caracteres
tags.form.color.label: Cor
tags.form.color.required: Por favor, insira uma cor
tags.form.color.invalid: Código hexadecimal formatado incorretamente.
tags.form.description.label: Descrição
tags.form.description.optional: (opcional)
tags.form.description.placeholder: 'Ex: Todos os contratos assinados pela empresa'
tags.form.description.max-length: A descrição deve ter menos de 256 caracteres
tags.form.no-description: Sem descrição
tags.table.headers.tag: Tag
tags.table.headers.description: Descrição
tags.table.headers.documents: Documentos
tags.table.headers.created: Criado em
tags.table.headers.actions: Ações

# Tagging rules

tagging-rules.field.name: nome do documento
tagging-rules.field.content: conteúdo do documento
tagging-rules.operator.equals: é igual a
tagging-rules.operator.not-equals: é diferente de
tagging-rules.operator.contains: contém
tagging-rules.operator.not-contains: não contém
tagging-rules.operator.starts-with: começa com
tagging-rules.operator.ends-with: termina com
tagging-rules.list.title: Regras de marcação
tagging-rules.list.description: Gerencie as regras de marcação da sua organização para aplicar tags automaticamente a documentos com base em condições definidas por você.
tagging-rules.list.demo-warning: 'Nota: Como este é um ambiente de demonstração (sem servidor), as regras de marcação não serão aplicadas a novos documentos adicionados.'
tagging-rules.list.no-tagging-rules.title: Nenhuma regra de marcação
tagging-rules.list.no-tagging-rules.description: Crie uma regra de marcação para aplicar tags automaticamente aos documentos adicionados, com base em condições definidas por você.
tagging-rules.list.no-tagging-rules.create-tagging-rule: Criar regra de marcação
tagging-rules.list.card.no-conditions: Nenhuma condição
tagging-rules.list.card.one-condition: 1 condição
tagging-rules.list.card.conditions: '{{ count }} condições'
tagging-rules.list.card.delete: Excluir regra
tagging-rules.list.card.edit: Editar regra
tagging-rules.create.title: Criar regra de marcação
tagging-rules.create.success: Regra de marcação criada com sucesso
tagging-rules.create.error: Falha ao criar a regra de marcação
tagging-rules.create.submit: Criar regra
tagging-rules.form.name.label: Nome
tagging-rules.form.name.placeholder: 'Exemplo: Marcar faturas'
tagging-rules.form.name.min-length: Por favor, insira um nome para a regra
tagging-rules.form.name.max-length: O nome deve ter menos de 64 caracteres
tagging-rules.form.description.label: Descrição
tagging-rules.form.description.placeholder: "Exemplo: Marcar documentos com 'fatura' no nome"
tagging-rules.form.description.max-length: A descrição deve ter menos de 256 caracteres
tagging-rules.form.conditions.label: Condições
tagging-rules.form.conditions.description: Defina as condições que devem ser atendidas para que a regra seja aplicada. Todas as condições devem ser atendidas.
tagging-rules.form.conditions.add-condition: Adicionar condição
tagging-rules.form.conditions.no-conditions.title: Nenhuma condição
tagging-rules.form.conditions.no-conditions.description: Você não adicionou nenhuma condição a esta regra. Ela será aplicada a todos os documentos.
tagging-rules.form.conditions.no-conditions.confirm: Aplicar regra sem condições
tagging-rules.form.conditions.no-conditions.cancel: Cancelar
tagging-rules.form.conditions.value.placeholder: 'Exemplo: fatura'
tagging-rules.form.conditions.value.min-length: Por favor, insira um valor para a condição
tagging-rules.form.tags.label: Tags
tagging-rules.form.tags.description: Selecione as tags que serão aplicadas aos documentos adicionados que correspondam às condições
tagging-rules.form.tags.min-length: Ao menos uma tag para aplicar é necessária
tagging-rules.form.tags.add-tag: Criar tag
tagging-rules.form.submit: Criar regra
tagging-rules.update.title: Atualizar regra de marcação
tagging-rules.update.error: Falha ao atualizar a regra de marcação
tagging-rules.update.submit: Atualizar regra
tagging-rules.update.cancel: Cancelar

# Intake emails

intake-emails.title: E-mails de entrada
intake-emails.description: Os endereços de e-mail de entrada são usados para importar automaticamente e-mails para o Papra. Basta encaminhar e-mails para o endereço de entrada e os anexos serão adicionados aos documentos da sua organização.
intake-emails.disabled.title: E-mails de entrada desativados
intake-emails.disabled.description: Os e-mails de entrada estão desativados nesta instância. Por favor, entre em contato com o administrador para ativá-los. Consulte a {{ documentation }} para mais informações.
intake-emails.disabled.documentation: documentação
intake-emails.info: Apenas e-mails de entrada habilitados e provenientes de origens permitidas serão processados. Você pode ativar ou desativar um e-mail de entrada a qualquer momento.
intake-emails.empty.title: Nenhum e-mail de entrada
intake-emails.empty.description: Gere um endereço de entrada para importar facilmente anexos de e-mails.
intake-emails.empty.generate: Gerar e-mail de entrada
intake-emails.count: '{{ count }} e-mail{{ plural }} de entrada para esta organização'
intake-emails.new: Novo e-mail de entrada
intake-emails.disabled-label: (Desativado)
intake-emails.no-origins: Nenhuma origem de e-mail permitida
intake-emails.allowed-origins: Permitido de {{ count }} endereço{{ plural }}
intake-emails.actions.enable: Ativar
intake-emails.actions.disable: Desativar
intake-emails.actions.manage-origins: Gerenciar endereços de origem
intake-emails.actions.delete: Excluir
intake-emails.delete.confirm.title: Excluir e-mail de entrada?
intake-emails.delete.confirm.message: Tem certeza de que deseja excluir este e-mail de entrada? Esta ação não poderá ser desfeita.
intake-emails.delete.confirm.confirm-button: Excluir e-mail de entrada
intake-emails.delete.confirm.cancel-button: Cancelar
intake-emails.delete.success: E-mail de entrada excluído
intake-emails.create.success: E-mail de entrada criado
intake-emails.update.success.enabled: E-mail de entrada ativado
intake-emails.update.success.disabled: E-mail de entrada desativado
intake-emails.allowed-origins.title: Origens permitidas
intake-emails.allowed-origins.description: Apenas e-mails enviados para {{ email }} a partir dessas origens serão processados. Se nenhuma origem for especificada, todos os e-mails serão descartados.
intake-emails.allowed-origins.add.label: Adicionar e-mail de origem permitida
intake-emails.allowed-origins.add.placeholder: 'Ex: <EMAIL>'
intake-emails.allowed-origins.add.button: Adicionar
intake-emails.allowed-origins.add.error.exists: Este e-mail já está nas origens permitidas para este e-mail de entrada

# API keys

api-keys.permissions.documents.title: Documentos
api-keys.permissions.documents.documents:create: Criar documentos
api-keys.permissions.documents.documents:read: Ler documentos
api-keys.permissions.documents.documents:update: Atualizar documentos
api-keys.permissions.documents.documents:delete: Excluir documentos
api-keys.permissions.tags.title: Tags
api-keys.permissions.tags.tags:create: Criar tags
api-keys.permissions.tags.tags:read: Ler tags
api-keys.permissions.tags.tags:update: Atualizar tags
api-keys.permissions.tags.tags:delete: Excluir tags
api-keys.create.title: Criar chave de API
api-keys.create.description: Crie uma nova chave de API para acessar a API do Papra.
api-keys.create.success: A chave de API foi criada com sucesso.
api-keys.create.back: Voltar para as chaves de API
api-keys.create.form.name.label: Nome
api-keys.create.form.name.placeholder: 'Exemplo: Minha chave de API'
api-keys.create.form.name.required: Por favor, insira um nome para a chave de API
api-keys.create.form.permissions.label: Permissões
api-keys.create.form.permissions.required: Por favor, selecione ao menos uma permissão
api-keys.create.form.submit: Criar chave de API
api-keys.create.created.title: Chave de API criada
api-keys.create.created.description: A chave de API foi criada com sucesso. Salve-a em um local seguro, pois ela não será exibida novamente.
api-keys.list.title: Chaves de API
api-keys.list.description: Gerencie suas chaves de API aqui.
api-keys.list.create: Criar chave de API
api-keys.list.empty.title: Nenhuma chave de API
api-keys.list.empty.description: Crie uma chave de API para acessar a API do Papra.
api-keys.list.card.last-used: Último uso
api-keys.list.card.never: Nunca
api-keys.list.card.created: Criada em
api-keys.delete.success: A chave de API foi excluída com sucesso
api-keys.delete.confirm.title: Excluir chave de API
api-keys.delete.confirm.message: Tem certeza de que deseja excluir esta chave de API? Esta ação não poderá ser desfeita.
api-keys.delete.confirm.confirm-button: Excluir
api-keys.delete.confirm.cancel-button: Cancelar

# Webhooks

webhooks.list.title: Webhooks
webhooks.list.description: Gerencie os webhooks da sua organização
webhooks.list.empty.title: Nenhum webhook
webhooks.list.empty.description: Crie seu primeiro webhook para começar a receber eventos
webhooks.list.create: Criar webhook
webhooks.list.card.last-triggered: Última ativação
webhooks.list.card.never: Nunca
webhooks.list.card.created: Criado em
webhooks.create.title: Criar webhook
webhooks.create.description: Crie um novo webhook para receber eventos
webhooks.create.success: Webhook criado com sucesso
webhooks.create.back: Voltar
webhooks.create.form.submit: Criar webhook
webhooks.create.form.name.label: Nome do webhook
webhooks.create.form.name.placeholder: Insira o nome do webhook
webhooks.create.form.name.required: O nome é obrigatório
webhooks.create.form.url.label: URL do Webhook
webhooks.create.form.url.placeholder: Insira a URL do webhook
webhooks.create.form.url.required: A URL é obrigatória
webhooks.create.form.url.invalid: URL inválida
webhooks.create.form.secret.label: Segredo
webhooks.create.form.secret.placeholder: Insira o segredo do webhook
webhooks.create.form.events.label: Eventos
webhooks.create.form.events.required: Adicione pelo menos um evento
webhooks.update.title: Editar webhook
webhooks.update.description: Atualize os detalhes do seu webhook
webhooks.update.success: Webhook atualizado com sucesso
webhooks.update.submit: Atualizar webhook
webhooks.update.cancel: Cancelar
webhooks.update.form.secret.placeholder: Insira um novo segredo
webhooks.update.form.secret.placeholder-redacted: '[Segredo ocultado]'
webhooks.update.form.rotate-secret.button: Rotacionar segredo
webhooks.delete.success: Webhook excluído com sucesso
webhooks.delete.confirm.title: Excluir webhook
webhooks.delete.confirm.message: Tem certeza de que deseja excluir este webhook?
webhooks.delete.confirm.confirm-button: Excluir
webhooks.delete.confirm.cancel-button: Cancelar

webhooks.events.documents.document:created.description: Documento criado
webhooks.events.documents.document:deleted.description: Documento excluído

# Navigation

layout.menu.home: Início
layout.menu.documents: Documentos
layout.menu.tags: Tags
layout.menu.tagging-rules: Regras de marcação
layout.menu.deleted-documents: Documentos excluídos
layout.menu.organization-settings: Configurações
layout.menu.api-keys: Chaves de API
layout.menu.settings: Configurações
layout.menu.account: Conta
layout.menu.general-settings: Configurações gerais
layout.menu.intake-emails: E-mails de entrada
layout.menu.webhooks: Webhooks
layout.menu.members: Membros
layout.menu.invitations: Convites

layout.theme.light: Tema claro
layout.theme.dark: Tema escuro
layout.theme.system: Tema do sistema

layout.search.placeholder: Buscar...
layout.menu.import-document: Importar um documento

user-menu.account-settings: Configurações da conta
user-menu.api-keys: Chaves de API
user-menu.invitations: Convites
user-menu.language: Idioma
user-menu.logout: Sair

# Command palette

command-palette.search.placeholder: Buscar comandos ou documentos
command-palette.no-results: Nenhum resultado encontrado
command-palette.sections.documents: Documentos
command-palette.sections.theme: Tema

# API errors

api-errors.document.already_exists: O documento já existe
api-errors.document.file_too_big: O arquivo do documento é muito grande
api-errors.intake_email.limit_reached: O número máximo de e-mails de entrada para esta organização foi atingido. Faça um upgrade no seu plano para criar mais e-mails de entrada.
api-errors.user.max_organization_count_reached: Você atingiu o número máximo de organizações que pode criar. Se precisar criar mais, entre em contato com o suporte.
api-errors.default: Ocorreu um erro ao processar sua solicitação.
api-errors.organization.invitation_already_exists: Já existe um convite para este e-mail nesta organização.
api-errors.user.already_in_organization: Este usuário já faz parte desta organização.
api-errors.user.organization_invitation_limit_reached: O número máximo de convites por hoje foi atingido. Por favor, tente novamente amanhã.
api-errors.demo.not_available: Este recurso não está disponível em ambiente de demonstração
api-errors.tags.already_exists: Já existe uma tag com este nome nesta organização

# Not found

not-found.title: 404 - Página não encontrada
not-found.description: Desculpe, a página que você está procurando não existe. Verifique o URL e tente novamente.
not-found.back-to-home: Voltar para a página inicial

# Demo

demo.popup.description: Este é um ambiente de demonstração; todos os dados são salvos no armazenamento local do seu navegador.
demo.popup.discord: Entre no {{ discordLink }} para obter suporte, sugerir funcionalidades ou apenas conversar.
demo.popup.discord-link-label: Comunidade do Discord
demo.popup.reset: Redefinir dados da demonstração
demo.popup.hide: Ocultar

# Color picker

color-picker.hue: Matiz
color-picker.saturation: Saturação
color-picker.lightness: Brilho
color-picker.select-color: Selecionar cor
color-picker.select-a-color: Selecione uma cor
