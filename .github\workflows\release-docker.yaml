name: Release new versions

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release'
        required: true
        type: string

permissions:
  contents: read
  packages: write

jobs:
  docker-release:
    name: Release Docker images
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push root Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./docker/Dockerfile
          platforms: linux/amd64,linux/arm64,linux/arm/v7
          push: true
          tags: |
            corentinth/papra:latest-root
            corentinth/papra:${{ inputs.version }}-root
            ghcr.io/papra-hq/papra:latest-root
            ghcr.io/papra-hq/papra:${{ inputs.version }}-root

      - name: Build and push rootless Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./docker/Dockerfile.rootless
          platforms: linux/amd64,linux/arm64,linux/arm/v7
          push: true
          tags: |
            corentinth/papra:latest
            corentinth/papra:latest-rootless
            corentinth/papra:${{ inputs.version }}-rootless
            ghcr.io/papra-hq/papra:latest
            ghcr.io/papra-hq/papra:latest-rootless
            ghcr.io/papra-hq/papra:${{ inputs.version }}-rootless