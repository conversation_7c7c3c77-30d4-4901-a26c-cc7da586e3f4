{"name": "@papra/lecture", "type": "module", "version": "0.0.7", "packageManager": "pnpm@9.15.0", "description": "A simple library to extract text from files", "author": "Corentin <PERSON> <<EMAIL>> (https://corentin.tech)", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/papra-hq/papra", "directory": "packages/lecture"}, "bugs": {"url": "https://github.com/papra-hq/papra/issues"}, "keywords": ["file", "text", "extract", "lecture", "document", "content"], "sideEffects": false, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"lint": "eslint .", "lint:fix": "eslint --fix .", "test": "vitest run", "test:watch": "vitest watch", "generate-fixtures": "vitest --update", "prepare": "pnpm run build", "build": "unbuild", "typecheck": "tsc --noEmit", "prepublishOnly": "pnpm run build"}, "dependencies": {"@corentinth/chisels": "^1.3.1", "tesseract.js": "^6.0.0", "unpdf": "^0.12.1"}, "devDependencies": {"@antfu/eslint-config": "catalog:", "@types/node": "catalog:", "@vitest/coverage-v8": "catalog:", "eslint": "catalog:", "mime": "^4.0.6", "tinyglobby": "^0.2.10", "typescript": "catalog:", "unbuild": "^3.3.1", "vitest": "catalog:"}}