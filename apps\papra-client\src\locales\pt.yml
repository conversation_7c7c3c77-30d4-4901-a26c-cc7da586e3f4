# Authentication

auth.request-password-reset.title: Redefinir a sua palavra-passe
auth.request-password-reset.description: Introduza o seu e-mail para redefinir a palavra-passe.
auth.request-password-reset.requested: Se existir uma conta para este e-mail, enviámos-lhe um e-mail para redefinir a palavra-passe.
auth.request-password-reset.back-to-login: Voltar ao início de sessão
auth.request-password-reset.form.email.label: E-mail
auth.request-password-reset.form.email.placeholder: 'Exemplo: <EMAIL>'
auth.request-password-reset.form.email.required: Por favor, introduza o seu endereço de e-mail
auth.request-password-reset.form.email.invalid: Este endereço de e-mail é inválido
auth.request-password-reset.form.submit: Solicitar redefinição de palavra-passe

auth.reset-password.title: Redefinir a sua palavra-passe
auth.reset-password.description: Introduza a sua nova palavra-passe para redefinir a palavra-passe.
auth.reset-password.reset: A sua palavra-passe foi redefinida.
auth.reset-password.back-to-login: Voltar ao início de sessão
auth.reset-password.form.new-password.label: Nova palavra-passe
auth.reset-password.form.new-password.placeholder: 'Exemplo: **********'
auth.reset-password.form.new-password.required: Por favor, introduza a sua nova palavra-passe
auth.reset-password.form.new-password.min-length: A palavra-passe deve ter pelo menos {{ minLength }} caracteres
auth.reset-password.form.new-password.max-length: A palavra-passe deve ter menos de {{ maxLength }} caracteres
auth.reset-password.form.submit: Redefinir palavra-passe

auth.email-provider.open: Abrir {{ provider }}

auth.login.title: Iniciar sessão no Papra
auth.login.description: Introduza o seu e-mail ou use o início de sessão social para aceder à sua conta Papra.
auth.login.login-with-provider: Iniciar sessão com {{ provider }}
auth.login.no-account: Não tem uma conta?
auth.login.register: Registar
auth.login.form.email.label: E-mail
auth.login.form.email.placeholder: 'Exemplo: <EMAIL>'
auth.login.form.email.required: Por favor, introduza o seu endereço de e-mail
auth.login.form.email.invalid: Este endereço de e-mail é inválido
auth.login.form.password.label: Palavra-passe
auth.login.form.password.placeholder: Definir uma palavra-passe
auth.login.form.password.required: Por favor, introduza a sua palavra-passe
auth.login.form.remember-me.label: Lembrar-me
auth.login.form.forgot-password.label: Esqueceu-se da palavra-passe?
auth.login.form.submit: Iniciar sessão

auth.register.title: Registar no Papra
auth.register.description: Crie uma conta para começar a usar o Papra.
auth.register.register-with-email: Registar com e-mail
auth.register.register-with-provider: Registar com {{ provider }}
auth.register.providers.google: Google
auth.register.providers.github: GitHub
auth.register.have-account: Já tem uma conta?
auth.register.login: Iniciar sessão
auth.register.registration-disabled.title: O registo está desativado
auth.register.registration-disabled.description: A criação de novas contas está atualmente desativada nesta instância do Papra. Apenas utilizadores com contas existentes podem iniciar sessão. Se acha que isto é um erro, contacte o administrador desta instância.
auth.register.form.email.label: E-mail
auth.register.form.email.placeholder: 'Exemplo: <EMAIL>'
auth.register.form.email.required: Por favor, introduza o seu endereço de e-mail
auth.register.form.email.invalid: Este endereço de e-mail é inválido
auth.register.form.password.label: Palavra-passe
auth.register.form.password.placeholder: Definir uma palavra-passe
auth.register.form.password.required: Por favor, introduza a sua palavra-passe
auth.register.form.password.min-length: A palavra-passe deve ter pelo menos {{ minLength }} caracteres
auth.register.form.password.max-length: A palavra-passe deve ter menos de {{ maxLength }} caracteres
auth.register.form.name.label: Nome
auth.register.form.name.placeholder: 'Exemplo: Ada Lovelace'
auth.register.form.name.required: Por favor, introduza o seu nome
auth.register.form.name.max-length: O nome deve ter menos de {{ maxLength }} caracteres
auth.register.form.submit: Registar

auth.email-validation-required.title: Verifique o seu e-mail
auth.email-validation-required.description: Foi enviado um e-mail de verificação para o seu endereço de e-mail. Por favor, verifique o seu endereço de e-mail clicando na ligação no e-mail.

auth.legal-links.description: Ao continuar, reconhece que compreende e concorda com os {{ terms }} e a {{ privacy }}.
auth.legal-links.terms: Termos de Serviço
auth.legal-links.privacy: Política de Privacidade

# User settings

user.settings.title: Definições do utilizador
user.settings.description: Gira as definições da sua conta aqui.

user.settings.email.title: Endereço de e-mail
user.settings.email.description: O seu endereço de e-mail não pode ser alterado.
user.settings.email.label: Endereço de e-mail

user.settings.name.title: Nome completo
user.settings.name.description: O seu nome completo é exibido a outros membros da organização.
user.settings.name.label: Nome completo
user.settings.name.placeholder: Ex. João Silva
user.settings.name.update: Atualizar nome
user.settings.name.updated: O seu nome completo foi atualizado

user.settings.logout.title: Terminar sessão
user.settings.logout.description: Terminar sessão da sua conta. Pode iniciar sessão novamente mais tarde.
user.settings.logout.button: Terminar sessão

# Organizations

organizations.list.title: As suas organizações
organizations.list.description: As organizações são uma forma de agrupar os seus documentos e gerir o acesso aos mesmos. Pode criar várias organizações e convidar os membros da sua equipa para colaborar.
organizations.list.create-new: Criar nova organização

organizations.details.no-documents.title: Sem documentos
organizations.details.no-documents.description: Não há documentos nesta organização ainda. Comece por carregar alguns documentos.
organizations.details.upload-documents: Carregar documentos
organizations.details.documents-count: documentos no total
organizations.details.total-size: tamanho total
organizations.details.latest-documents: Últimos documentos importados

organizations.create.title: Criar uma nova organização
organizations.create.description: Os seus documentos serão agrupados por organização. Pode criar várias organizações para separar os seus documentos, por exemplo, para documentos pessoais e de trabalho.
organizations.create.back: Voltar
organizations.create.error.max-count-reached: Atingiu o número máximo de organizações que pode criar, se precisar de criar mais, contacte o suporte.
organizations.create.form.name.label: Nome da organização
organizations.create.form.name.placeholder: Ex. Acme Inc.
organizations.create.form.name.required: Por favor, introduza um nome para a organização
organizations.create.form.submit: Criar organização
organizations.create.success: Organização criada com sucesso

organizations.create-first.title: Criar a sua organização
organizations.create-first.description: Os seus documentos serão agrupados por organização. Pode criar várias organizações para separar os seus documentos, por exemplo, para documentos pessoais e de trabalho.
organizations.create-first.default-name: A minha organização
organizations.create-first.user-name: 'Organização de {{ name }}'

organization.settings.title: Definições da Organização
organization.settings.page.title: Definições da organização
organization.settings.page.description: Gira as definições da sua organização aqui.
organization.settings.name.title: Nome da organização
organization.settings.name.update: Atualizar nome
organization.settings.name.placeholder: Ex. Acme Inc.
organization.settings.name.updated: Nome da organização atualizado
organization.settings.subscription.title: Subscrição
organization.settings.subscription.description: Gira a sua faturação, faturas e métodos de pagamento.
organization.settings.subscription.manage: Gerir subscrição
organization.settings.subscription.error: Falha ao obter URL do portal do cliente
organization.settings.delete.title: Eliminar organização
organization.settings.delete.description: Eliminar esta organização removerá permanentemente todos os dados associados à mesma.
organization.settings.delete.confirm.title: Eliminar organização
organization.settings.delete.confirm.message: Tem a certeza de que quer eliminar esta organização? Esta ação não pode ser desfeita e todos os dados associados a esta organização serão permanentemente removidos.
organization.settings.delete.confirm.confirm-button: Eliminar organização
organization.settings.delete.confirm.cancel-button: Cancelar
organization.settings.delete.success: Organização eliminada

organizations.members.title: Membros
organizations.members.description: Gira os membros da sua organização
organizations.members.invite-member: Convidar membro
organizations.members.invite-member-disabled-tooltip: Apenas administradores ou proprietários podem convidar membros para a organização
organizations.members.remove-from-organization: Remover da organização
organizations.members.role: Função
organizations.members.roles.owner: Proprietário
organizations.members.roles.admin: Administrador
organizations.members.roles.member: Membro
organizations.members.delete.confirm.title: Remover membro
organizations.members.delete.confirm.message: Tem a certeza de que quer remover este membro da organização?
organizations.members.delete.confirm.confirm-button: Remover
organizations.members.delete.confirm.cancel-button: Cancelar
organizations.members.delete.success: Membro removido da organização
organizations.members.update-role.success: Função do membro atualizada
organizations.members.table.headers.name: Nome
organizations.members.table.headers.email: E-mail
organizations.members.table.headers.role: Função
organizations.members.table.headers.created: Criado
organizations.members.table.headers.actions: Ações

organizations.invite-member.title: Convidar membro
organizations.invite-member.description: Convide um membro para a sua organização
organizations.invite-member.form.email.label: E-mail
organizations.invite-member.form.email.placeholder: 'Exemplo: <EMAIL>'
organizations.invite-member.form.email.required: Por favor, introduza um endereço de e-mail válido
organizations.invite-member.form.role.label: Função
organizations.invite-member.form.submit: Convidar para a organização
organizations.invite-member.success.message: Membro convidado
organizations.invite-member.success.description: O e-mail foi convidado para a organização.
organizations.invite-member.error.message: Falha ao convidar membro

organizations.invitations.title: Convites
organizations.invitations.description: Gira os convites da sua organização
organizations.invitations.list.cta: Convidar membro
organizations.invitations.list.empty.title: Sem convites pendentes
organizations.invitations.list.empty.description: Ainda não foi convidado para nenhuma organização.
organizations.invitations.status.pending: Pendente
organizations.invitations.status.accepted: Aceite
organizations.invitations.status.rejected: Rejeitado
organizations.invitations.status.expired: Expirado
organizations.invitations.status.cancelled: Cancelado
organizations.invitations.resend: Reenviar convite
organizations.invitations.cancel.title: Cancelar convite
organizations.invitations.cancel.description: Tem a certeza de que quer cancelar este convite?
organizations.invitations.cancel.confirm: Cancelar convite
organizations.invitations.cancel.cancel: Cancelar
organizations.invitations.resend.title: Reenviar convite
organizations.invitations.resend.description: Tem a certeza de que quer reenviar este convite? Isto enviará um novo e-mail ao destinatário.
organizations.invitations.resend.confirm: Reenviar convite
organizations.invitations.resend.cancel: Cancelar

invitations.list.title: Convites
invitations.list.description: Gira os convites da sua organização
invitations.list.empty.title: Sem convites pendentes
invitations.list.empty.description: Ainda não foi convidado para nenhuma organização.
invitations.list.headers.organization: Organização
invitations.list.headers.status: Estado
invitations.list.headers.created: Criado
invitations.list.headers.actions: Ações
invitations.list.actions.accept: Aceitar
invitations.list.actions.reject: Rejeitar
invitations.list.actions.accept.success.message: Convite aceite
invitations.list.actions.accept.success.description: O convite foi aceite.
invitations.list.actions.reject.success.message: Convite rejeitado
invitations.list.actions.reject.success.description: O convite foi rejeitado.

# Documents

documents.list.title: Documentos
documents.list.no-documents.title: Sem documentos
documents.list.no-documents.description: Não há documentos nesta organização ainda. Comece por carregar alguns documentos.
documents.list.no-results: Nenhum documento encontrado

documents.tabs.info: Informação
documents.tabs.content: Conteúdo
documents.tabs.activity: Atividade
documents.deleted.message: Este documento foi eliminado e será permanentemente removido em {{ days }} dias.
documents.actions.download: Descarregar
documents.actions.open-in-new-tab: Abrir em novo separador
documents.actions.restore: Restaurar
documents.actions.delete: Eliminar
documents.actions.edit: Editar
documents.actions.cancel: Cancelar
documents.actions.save: Guardar
documents.actions.saving: A guardar...
documents.content.alert: O conteúdo do documento é automaticamente extraído do documento no carregamento. É usado apenas para fins de pesquisa e indexação.
documents.info.id: ID
documents.info.name: Nome
documents.info.type: Tipo
documents.info.size: Tamanho
documents.info.created-at: Criado em
documents.info.updated-at: Atualizado em
documents.info.never: Nunca

documents.rename.title: Renomear documento
documents.rename.form.name.label: Nome
documents.rename.form.name.placeholder: 'Exemplo: Fatura 2024'
documents.rename.form.name.required: Por favor, introduza um nome para o documento
documents.rename.form.name.max-length: O nome deve ter menos de 255 caracteres
documents.rename.form.submit: Renomear documento
documents.rename.success: Documento renomeado com sucesso
documents.rename.cancel: Cancelar

import-documents.title.error: '{{ count }} documentos falharam'
import-documents.title.success: '{{ count }} documentos importados'
import-documents.title.pending: '{{ count }} / {{ total }} documentos importados'
import-documents.title.none: Importar documentos
import-documents.no-import-in-progress: Nenhuma importação de documento em progresso

documents.deleted.title: Documentos eliminados
documents.deleted.empty.title: Sem documentos eliminados
documents.deleted.empty.description: Não tem documentos eliminados. Os documentos que são eliminados serão movidos para a reciclagem por {{ days }} dias.
documents.deleted.retention-notice: Todos os documentos eliminados são armazenados na reciclagem por {{ days }} dias. Passando este prazo, os documentos serão permanentemente eliminados e não poderá restaurá-los.
documents.deleted.deleted-at: Eliminado
documents.deleted.restoring: A restaurar...
documents.deleted.deleting: A eliminar...

documents.preview.unknown-file-type: Não há pré-visualização disponível para este tipo de ficheiro
documents.preview.binary-file: Este parece ser um ficheiro binário e não pode ser exibido como texto

trash.delete-all.button: Eliminar tudo
trash.delete-all.confirm.title: Eliminar permanentemente todos os documentos?
trash.delete-all.confirm.description: Tem a certeza de que quer eliminar permanentemente todos os documentos da reciclagem? Esta ação não pode ser desfeita.
trash.delete-all.confirm.label: Eliminar
trash.delete-all.confirm.cancel: Cancelar
trash.delete.button: Eliminar
trash.delete.confirm.title: Eliminar documento permanentemente?
trash.delete.confirm.description: Tem a certeza de que quer eliminar permanentemente este documento da reciclagem? Esta ação não pode ser desfeita.
trash.delete.confirm.label: Eliminar
trash.delete.confirm.cancel: Cancelar
trash.deleted.success.title: Documento eliminado
trash.deleted.success.description: O documento foi eliminado permanentemente.

activity.document.created: O documento foi criado
activity.document.updated.single: O {{ field }} foi atualizado
activity.document.updated.multiple: Os {{ fields }} foram atualizados
activity.document.updated: O documento foi atualizado
activity.document.deleted: O documento foi eliminado
activity.document.restored: O documento foi restaurado
activity.document.tagged: A etiqueta {{ tag }} foi adicionada
activity.document.untagged: A etiqueta {{ tag }} foi removida

activity.document.user.name: por {{ name }}

activity.load-more: Carregar mais
activity.no-more-activities: Não há mais atividades para este documento

# Tags

tags.no-tags.title: Ainda sem etiquetas
tags.no-tags.description: Esta organização ainda não tem etiquetas. As etiquetas são usadas para categorizar documentos. Pode adicionar etiquetas aos seus documentos para os tornar mais fáceis de encontrar e organizar.
tags.no-tags.create-tag: Criar etiqueta

tags.title: Etiquetas de Documentos
tags.description: As etiquetas são usadas para categorizar documentos. Pode adicionar etiquetas aos seus documentos para os tornar mais fáceis de encontrar e organizar.
tags.create: Criar etiqueta
tags.update: Atualizar etiqueta
tags.delete: Eliminar etiqueta
tags.delete.confirm.title: Eliminar etiqueta
tags.delete.confirm.message: Tem a certeza de que quer eliminar esta etiqueta? Eliminar uma etiqueta irá removê-la de todos os documentos.
tags.delete.confirm.confirm-button: Eliminar
tags.delete.confirm.cancel-button: Cancelar
tags.delete.success: Etiqueta eliminada com sucesso
tags.create.success: Etiqueta "{{ name }}" criada com sucesso.
tags.update.success: Etiqueta "{{ name }}" atualizada com sucesso.
tags.form.name.label: Nome
tags.form.name.placeholder: Ex. Contratos
tags.form.name.required: Por favor, introduza um nome para a etiqueta
tags.form.name.max-length: O nome da etiqueta deve ter menos de 64 caracteres
tags.form.color.label: Cor
tags.form.color.required: Por favor, introduza uma cor
tags.form.color.invalid: A cor hexadecimal está mal formatada.
tags.form.description.label: Descrição
tags.form.description.optional: (opcional)
tags.form.description.placeholder: Ex. Todos os contratos assinados pela empresa
tags.form.description.max-length: A descrição deve ter menos de 256 caracteres
tags.form.no-description: Sem descrição
tags.table.headers.tag: Etiqueta
tags.table.headers.description: Descrição
tags.table.headers.documents: Documentos
tags.table.headers.created: Criado
tags.table.headers.actions: Ações

# Tagging rules

tagging-rules.field.name: nome do documento
tagging-rules.field.content: conteúdo do documento
tagging-rules.operator.equals: igual a
tagging-rules.operator.not-equals: não igual a
tagging-rules.operator.contains: contém
tagging-rules.operator.not-contains: não contém
tagging-rules.operator.starts-with: começa com
tagging-rules.operator.ends-with: termina com
tagging-rules.list.title: Regras de etiquetagem
tagging-rules.list.description: Gira as regras de etiquetagem da sua organização, para etiquetar automaticamente documentos com base em condições que define.
tagging-rules.list.demo-warning: 'Nota: Como este é um ambiente de demonstração (sem servidor), as regras de etiquetagem não serão aplicadas a documentos recém-adicionados.'
tagging-rules.list.no-tagging-rules.title: Sem regras de etiquetagem
tagging-rules.list.no-tagging-rules.description: Crie uma regra de etiquetagem para etiquetar automaticamente os seus documentos adicionados com base em condições que define.
tagging-rules.list.no-tagging-rules.create-tagging-rule: Criar regra de etiquetagem
tagging-rules.list.card.no-conditions: Sem condições
tagging-rules.list.card.one-condition: 1 condição
tagging-rules.list.card.conditions: '{{ count }} condições'
tagging-rules.list.card.delete: Eliminar regra
tagging-rules.list.card.edit: Editar regra
tagging-rules.create.title: Criar regra de etiquetagem
tagging-rules.create.success: Regra de etiquetagem criada com sucesso
tagging-rules.create.error: Falha ao criar regra de etiquetagem
tagging-rules.create.submit: Criar regra
tagging-rules.form.name.label: Nome
tagging-rules.form.name.placeholder: 'Exemplo: Etiquetar faturas'
tagging-rules.form.name.min-length: Por favor, introduza um nome para a regra
tagging-rules.form.name.max-length: O nome deve ter menos de 64 caracteres
tagging-rules.form.description.label: Descrição
tagging-rules.form.description.placeholder: "Exemplo: Etiquetar documentos com 'fatura' no nome"
tagging-rules.form.description.max-length: A descrição deve ter menos de 256 caracteres
tagging-rules.form.conditions.label: Condições
tagging-rules.form.conditions.description: Defina as condições que devem ser cumpridas para a regra se aplicar. Todas as condições devem ser cumpridas para a regra se aplicar.
tagging-rules.form.conditions.add-condition: Adicionar condição
tagging-rules.form.conditions.no-conditions.title: Sem condições
tagging-rules.form.conditions.no-conditions.description: Não adicionou nenhuma condição a esta regra. Esta regra aplicará as suas etiquetas a todos os documentos.
tagging-rules.form.conditions.no-conditions.confirm: Aplicar regra sem condições
tagging-rules.form.conditions.no-conditions.cancel: Cancelar
tagging-rules.form.conditions.value.placeholder: 'Exemplo: fatura'
tagging-rules.form.conditions.value.min-length: Por favor, introduza um valor para a condição
tagging-rules.form.tags.label: Etiquetas
tagging-rules.form.tags.description: Selecione as etiquetas a aplicar aos documentos adicionados que correspondem às condições
tagging-rules.form.tags.min-length: É necessária pelo menos uma etiqueta para aplicar
tagging-rules.form.tags.add-tag: Criar etiqueta
tagging-rules.form.submit: Criar regra
tagging-rules.update.title: Atualizar regra de etiquetagem
tagging-rules.update.error: Falha ao atualizar regra de etiquetagem
tagging-rules.update.submit: Atualizar regra
tagging-rules.update.cancel: Cancelar

# Intake emails

intake-emails.title: E-mails de Receção
intake-emails.description: Os endereços de e-mail de receção são usados para ingerir automaticamente e-mails no Papra. Basta reencaminhar e-mails para o endereço de e-mail de receção e os seus anexos serão adicionados aos documentos da sua organização.
intake-emails.disabled.title: Os E-mails de Receção estão desativados
intake-emails.disabled.description: Os e-mails de receção estão desativados nesta instância. Contacte o seu administrador para os ativar. Consulte a {{ documentation }} para mais informações.
intake-emails.disabled.documentation: documentação
intake-emails.info: Apenas e-mails de receção ativados de origens permitidas serão processados. Pode ativar ou desativar um e-mail de receção a qualquer momento.
intake-emails.empty.title: Sem e-mails de receção
intake-emails.empty.description: Gere um endereço de receção para ingerir facilmente anexos de e-mails.
intake-emails.empty.generate: Gerar e-mail de receção
intake-emails.count: '{{ count }} e-mail{{ plural }} de receção para esta organização'
intake-emails.new: Novo e-mail de receção
intake-emails.disabled-label: (Desativado)
intake-emails.no-origins: Sem origens de e-mail permitidas
intake-emails.allowed-origins: Permitido de {{ count }} endereço{{ plural }}
intake-emails.actions.enable: Ativar
intake-emails.actions.disable: Desativar
intake-emails.actions.manage-origins: Gerir endereços de origem
intake-emails.actions.delete: Eliminar
intake-emails.delete.confirm.title: Eliminar e-mail de receção?
intake-emails.delete.confirm.message: Tem a certeza de que quer eliminar este e-mail de receção? Esta ação não pode ser desfeita.
intake-emails.delete.confirm.confirm-button: Eliminar e-mail de receção
intake-emails.delete.confirm.cancel-button: Cancelar
intake-emails.delete.success: E-mail de receção eliminado
intake-emails.create.success: E-mail de receção criado
intake-emails.update.success.enabled: E-mail de receção ativado
intake-emails.update.success.disabled: E-mail de receção desativado
intake-emails.allowed-origins.title: Origens permitidas
intake-emails.allowed-origins.description: Apenas e-mails enviados para {{ email }} destas origens serão processados. Se nenhuma origem for especificada, todos os e-mails serão descartados.
intake-emails.allowed-origins.add.label: Adicionar e-mail de origem permitida
intake-emails.allowed-origins.add.placeholder: Ex. <EMAIL>
intake-emails.allowed-origins.add.button: Adicionar
intake-emails.allowed-origins.add.error.exists: Este e-mail já está nas origens permitidas para este e-mail de receção

# API keys

api-keys.permissions.documents.title: Documentos
api-keys.permissions.documents.documents:create: Criar documentos
api-keys.permissions.documents.documents:read: Ler documentos
api-keys.permissions.documents.documents:update: Atualizar documentos
api-keys.permissions.documents.documents:delete: Eliminar documentos
api-keys.permissions.tags.title: Etiquetas
api-keys.permissions.tags.tags:create: Criar etiquetas
api-keys.permissions.tags.tags:read: Ler etiquetas
api-keys.permissions.tags.tags:update: Atualizar etiquetas
api-keys.permissions.tags.tags:delete: Eliminar etiquetas
api-keys.create.title: Criar chave API
api-keys.create.description: Crie uma nova chave API para aceder à API do Papra.
api-keys.create.success: A chave API foi criada com sucesso.
api-keys.create.back: Voltar às chaves API
api-keys.create.form.name.label: Nome
api-keys.create.form.name.placeholder: 'Exemplo: A minha chave API'
api-keys.create.form.name.required: Por favor, introduza um nome para a chave API
api-keys.create.form.permissions.label: Permissões
api-keys.create.form.permissions.required: Por favor, selecione pelo menos uma permissão
api-keys.create.form.submit: Criar chave API
api-keys.create.created.title: Chave API criada
api-keys.create.created.description: A chave API foi criada com sucesso. Guarde-a num local seguro pois não será exibida novamente.
api-keys.list.title: Chaves API
api-keys.list.description: Gira as suas chaves API aqui.
api-keys.list.create: Criar chave API
api-keys.list.empty.title: Sem chaves API
api-keys.list.empty.description: Crie uma chave API para aceder à API do Papra.
api-keys.list.card.last-used: Última utilização
api-keys.list.card.never: Nunca
api-keys.list.card.created: Criado
api-keys.delete.success: A chave API foi eliminada com sucesso
api-keys.delete.confirm.title: Eliminar chave API
api-keys.delete.confirm.message: Tem a certeza de que quer eliminar esta chave API? Esta ação não pode ser desfeita.
api-keys.delete.confirm.confirm-button: Eliminar
api-keys.delete.confirm.cancel-button: Cancelar

# Webhooks

webhooks.list.title: Webhooks
webhooks.list.description: Gira os webhooks da sua organização
webhooks.list.empty.title: Nenhum webhook
webhooks.list.empty.description: Crie o seu primeiro webhook para começar a receber eventos
webhooks.list.create: Criar webhook
webhooks.list.card.last-triggered: Última ativação
webhooks.list.card.never: Nunca
webhooks.list.card.created: Criado em
webhooks.create.title: Criar webhook
webhooks.create.description: Crie um novo webhook para receber eventos
webhooks.create.success: Webhook criado com sucesso
webhooks.create.back: Voltar
webhooks.create.form.submit: Criar webhook
webhooks.create.form.name.label: Nome do webhook
webhooks.create.form.name.placeholder: Insira o nome do webhook
webhooks.create.form.name.required: O nome é obrigatório
webhooks.create.form.url.label: URL do Webhook
webhooks.create.form.url.placeholder: Insira o URL do webhook
webhooks.create.form.url.required: O URL é obrigatória
webhooks.create.form.url.invalid: URL inválido
webhooks.create.form.secret.label: Segredo
webhooks.create.form.secret.placeholder: Insira o segredo do webhook
webhooks.create.form.events.label: Eventos
webhooks.create.form.events.required: Adicione pelo menos um evento
webhooks.update.title: Editar webhook
webhooks.update.description: Atualize os detalhes do seu webhook
webhooks.update.success: Webhook atualizado com sucesso
webhooks.update.submit: Atualizar webhook
webhooks.update.cancel: Cancelar
webhooks.update.form.secret.placeholder: Insira um novo segredo
webhooks.update.form.secret.placeholder-redacted: '[Segredo ocultado]'
webhooks.update.form.rotate-secret.button: Rotacionar segredo
webhooks.delete.success: Webhook eliminado com sucesso
webhooks.delete.confirm.title: Eliminar webhook
webhooks.delete.confirm.message: Tem a certeza de que deseja eliminar este webhook?
webhooks.delete.confirm.confirm-button: Eliminar
webhooks.delete.confirm.cancel-button: Cancelar

webhooks.events.documents.document:created.description: Documento criado
webhooks.events.documents.document:deleted.description: Documento eliminado

# Navigation

layout.menu.home: Início
layout.menu.documents: Documentos
layout.menu.tags: Tags
layout.menu.tagging-rules: Regras de etiquetagem
layout.menu.deleted-documents: Documentos eliminados
layout.menu.organization-settings: Definições
layout.menu.api-keys: Chaves API
layout.menu.settings: Definições
layout.menu.account: Conta
layout.menu.general-settings: Definições gerais
layout.menu.intake-emails: E-mails de entrada
layout.menu.webhooks: Webhooks
layout.menu.members: Membros
layout.menu.invitations: Convites

layout.theme.light: Tema claro
layout.theme.dark: Tema escuro
layout.theme.system: Tema do sistema

layout.search.placeholder: Procurar...
layout.menu.import-document: Importar um documento

user-menu.account-settings: Definições da conta
user-menu.api-keys: Chaves API
user-menu.invitations: Convites
user-menu.language: Linguagem
user-menu.logout: Sair

# Command palette

command-palette.search.placeholder: Procurar comandos ou documentos
command-palette.no-results: Nenhum resultado encontrado
command-palette.sections.documents: Documentos
command-palette.sections.theme: Tema

# API errors

api-errors.document.already_exists: O documento já existe
api-errors.document.file_too_big: O arquivo do documento é muito grande
api-errors.intake_email.limit_reached: O número máximo de e-mails de entrada para esta organização foi atingido. Faça um upgrade no seu plano para criar mais e-mails de entrada.
api-errors.user.max_organization_count_reached: Atingiu o número máximo de organizações que pode criar. Se precisar de criar mais, entre em contato com o suporte.
api-errors.default: Ocorreu um erro ao processar a solicitação.
api-errors.organization.invitation_already_exists: Já existe um convite para este e-mail nesta organização.
api-errors.user.already_in_organization: Este utilizadpr já faz parte desta organização.
api-errors.user.organization_invitation_limit_reached: O número máximo de convites por hoje foi atingido. Por favor, tente novamente amanhã.
api-errors.demo.not_available: Este recurso não está disponível em ambiente de demonstração
api-errors.tags.already_exists: Já existe uma etiqueta com este nome nesta organização

# Not found

not-found.title: 404 - Página não encontrada
not-found.description: Desculpe, a página que procura não existe. Verifique o URL e tente novamente.
not-found.back-to-home: Voltar para a página inicial

# Demo

demo.popup.description: Este é um ambiente de demonstração; todos os dados são guardadis no armazenamento local do navegador.
demo.popup.discord: Entre no {{ discordLink }} para obter suporte, sugerir funcionalidades ou apenas conversar.
demo.popup.discord-link-label: Comunidade do Discord
demo.popup.reset: Redefinir dados da demonstração
demo.popup.hide: Ocultar

# Color picker

color-picker.hue: Matiz
color-picker.saturation: Saturação
color-picker.lightness: Brilho
color-picker.select-color: Selecionar cor
color-picker.select-a-color: Selecione uma cor
