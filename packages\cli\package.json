{"name": "@papra/cli", "type": "module", "version": "0.0.1", "packageManager": "pnpm@10.12.3", "description": "Command line interface for Papra, the document archiving platform.", "author": "Corentin <PERSON> <<EMAIL>> (https://corentin.tech)", "license": "AGPL-3.0-or-later", "repository": {"type": "git", "url": "https://github.com/papra-hq/papra", "directory": "packages/cli"}, "bugs": {"url": "https://github.com/papra-hq/papra/issues"}, "keywords": ["papra", "cli", "document", "archiving", "storage"], "main": "./dist/cli.cjs", "module": "./dist/cli.mjs", "bin": {"papra": "./bin/papra.mjs"}, "files": ["bin", "dist"], "scripts": {"dev": "tsx ./src/cli.ts", "lint": "eslint .", "lint:fix": "eslint --fix .", "test": "vitest run", "test:watch": "vitest watch", "typecheck": "tsc --noEmit", "build": "unbuild"}, "dependencies": {"@clack/prompts": "^0.10.1", "@corentinth/chisels": "^1.3.1", "@papra/api-sdk": "workspace:*", "citty": "^0.1.6", "conf": "^13.1.0", "mime-types": "^3.0.1", "ofetch": "^1.4.1", "valibot": "1.0.0-beta.10"}, "devDependencies": {"@antfu/eslint-config": "catalog:", "@types/mime-types": "^2.1.4", "@types/node": "catalog:", "eslint": "catalog:", "tsx": "^4.19.3", "typescript": "catalog:", "unbuild": "^3.5.0", "vitest": "catalog:"}}