import { describe, expect, test } from 'vitest';
import { buildEmailAddress, getEmailUsername, getIsFromAllowedOrigin, parseEmailAddress } from './intake-emails.models';

describe('intake-emails models', () => {
  describe('getEmailUsername', () => {
    test('the username is the whole part of the email address before the @', () => {
      expect(getEmailUsername({ email: '<EMAIL>' })).to.eql({ username: 'foo' });
      expect(getEmailUsername({ email: '<EMAIL>' })).to.eql({ username: 'foo+bar' });
      expect(getEmailUsername({ email: undefined })).to.eql({ username: undefined });
    });
  });

  describe('getIsFromAllowedOrigin', () => {
    test('an origin is allowed if it is in the list of the intake email allowed origins email address', () => {
      expect(
        getIsFromAllowedOrigin({
          origin: '<EMAIL>',
          allowedOrigins: ['<EMAIL>', '<EMAIL>'],
        }),
      ).to.eql(true);

      expect(
        getIsFromAllowedOrigin({
          origin: '<EMAIL>',
          allowedOrigins: ['<EMAIL>'],
        }),
      ).to.eql(false);
    });

    test('email addresses are case insensitive', () => {
      expect(
        getIsFromAllowedOrigin({
          origin: '<EMAIL>',
          allowedOrigins: ['<EMAIL>'],
        }),
      ).to.eql(true);
    });

    test('if no allowed origins are provided, the origin is not allowed', () => {
      expect(
        getIsFromAllowedOrigin({
          origin: '<EMAIL>',
          allowedOrigins: [],
        }),
      ).to.eql(false);
    });
  });

  describe('buildEmailAddress', () => {
    test('it builds an email address from a username, a domain and an optional plus part', () => {
      expect(buildEmailAddress({ username: 'foo', domain: 'example.fr' })).to.eql('<EMAIL>');
      expect(buildEmailAddress({ username: 'foo', domain: 'example.fr', plusPart: 'bar' })).to.eql('<EMAIL>');
    });
  });

  describe('parseEmailAddress', () => {
    test('it parses an email address into a username, a domain and an optional plus part', () => {
      expect(parseEmailAddress({ email: '<EMAIL>' })).to.eql({ username: 'foo', domain: 'example.fr', plusPart: undefined });
      expect(parseEmailAddress({ email: '<EMAIL>' })).to.eql({ username: 'foo', domain: 'example.fr', plusPart: 'bar' });
      expect(parseEmailAddress({ email: '<EMAIL>' })).to.eql({ username: 'foo', domain: 'example.fr', plusPart: 'bar+baz' });
    });
  });
});
