import type { ConfigDefinition, ConfigDefinitionElement } from 'figue';
import { isArray, isEmpty, isNil } from 'lodash-es';
import { marked } from 'marked';

import { configDefinition } from '../../papra-server/src/modules/config/config';

function walk(configDefinition: ConfigDefinition, path: string[] = []): (ConfigDefinitionElement & { path: string[] })[] {
  return Object
    .entries(configDefinition)
    .flatMap(([key, value]) => {
      if ('schema' in value) {
        return [{ ...value, path: [...path, key] }] as (ConfigDefinitionElement & { path: string[] })[];
      }

      return walk(value, [...path, key]);
    });
}

const configDetails = walk(configDefinition);

function formatDoc(doc: string | undefined): string {
  const coerced = (doc ?? '').trim();

  if (coerced.endsWith('.')) {
    return coerced;
  }

  return `${coerced}.`;
}

const rows = configDetails
  .filter(({ path }) => path[0] !== 'env')
  .map(({ doc, default: defaultValue, env, path }) => {
    const isEmptyDefaultValue = isNil(defaultValue) || (isArray(defaultValue) && isEmpty(defaultValue)) || defaultValue === '';

    const rawDocumentation = formatDoc(doc);

    // The client baseUrl default value is overridden in the Dockerfiles
    const defaultOverride = path.join('.') === 'client.baseUrl' ? 'http://localhost:1221' : undefined;

    return {
      path,
      env,
      documentation: rawDocumentation,
      defaultValue: defaultOverride ?? (isEmptyDefaultValue ? undefined : defaultValue),
    };
  });

const mdSections = rows.map(({ documentation, env, path, defaultValue }) => `
### ${env}
${documentation}

- Path: \`${path.join('.')}\`
- Environment variable: \`${env}\`
- Default value: \`${defaultValue}\`


`.trim()).join('\n\n---\n\n');

function wrapText(text: string, maxLength = 75) {
  const words = text.split(' ');
  const lines: string[] = [];
  let currentLine = '';

  words.forEach((word) => {
    if ((currentLine + word).length + 1 <= maxLength) {
      currentLine += (currentLine ? ' ' : '') + word;
    } else {
      lines.push(currentLine);
      currentLine = word;
    }
  });

  if (currentLine) {
    lines.push(currentLine);
  }

  return lines.map(line => `# ${line}`);
}

const fullDotEnv = rows.map(({ env, defaultValue, documentation }) => {
  const isEmptyDefaultValue = isNil(defaultValue) || (isArray(defaultValue) && isEmpty(defaultValue)) || defaultValue === '';

  return [
    ...wrapText(documentation),
    `# ${env}=${isEmptyDefaultValue ? '' : defaultValue}`,
  ].join('\n');
}).join('\n\n');

// Dirty hack to add the same anchors to the headings as the ones generated by Starlight
const renderer = new marked.Renderer();
renderer.heading = function ({ text, depth }) {
  const slug = text.toLowerCase().replace(/\W+/g, '-');
  return `
<div class="sl-heading-wrapper level-h${depth}">
<h${depth} id="${slug}">${text}</h${depth}>
<a class="sl-anchor-link" href="#${slug}"><span aria-hidden="true" class="sl-anchor-icon"><svg width="16" height="16" viewBox="0 0 24 24"><path fill="currentcolor" d="m12.11 15.39-3.88 3.88a2.52 2.52 0 0 1-3.5 0 2.47 2.47 0 0 1 0-3.5l3.88-3.88a1 1 0 0 0-1.42-1.42l-3.88 3.89a4.48 4.48 0 0 0 6.33 6.33l3.89-3.88a1 1 0 1 0-1.42-1.42Zm8.58-12.08a4.49 4.49 0 0 0-6.33 0l-3.89 3.88a1 1 0 0 0 1.42 1.42l3.88-3.88a2.52 2.52 0 0 1 3.5 0 2.47 2.47 0 0 1 0 3.5l-3.88 3.88a1 1 0 1 0 1.42 1.42l3.88-3.89a4.49 4.49 0 0 0 0-6.33ZM8.83 15.17a1 1 0 0 0 1.1.22 1 1 0 0 0 .32-.22l4.92-4.92a1 1 0 0 0-1.42-1.42l-4.92 4.92a1 1 0 0 0 0 1.42Z"></path></svg></span><span class="sr-only">Section titled “Configuration files”</span></a>
</div>
`.trim().replace(/\n/g, '');
};

const sectionsHtml = marked.parse(mdSections, { renderer });

export { fullDotEnv, mdSections, sectionsHtml };
