{"name": "@papra/docs", "version": "0.3.1", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "@papra/docs", "version": "0.3.1", "license": "AGPL-3.0-or-later", "dependencies": {"@astrojs/solid-js": "^5.1.0", "@astrojs/starlight": "^0.34.3", "@kobalte/core": "^0.13.9", "astro": "^5.8.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "sharp": "^0.32.5", "shiki": "^3.4.2", "solid-js": "^1.9.7", "starlight-links-validator": "^0.16.0", "starlight-theme-rapide": "^0.5.0", "tailwind-merge": "^2.6.0", "unocss-preset-animations": "^1.2.1", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@antfu/eslint-config": "^3.13.0", "@iconify-json/tabler": "^1.1.120", "@types/lodash-es": "^4.17.12", "@unocss/reset": "^0.64.0", "eslint": "^9.17.0", "eslint-plugin-astro": "^1.3.1", "figue": "^2.2.2", "lodash-es": "^4.17.21", "marked": "^15.0.6", "typescript": "^5.7.3", "unocss": "0.65.0-beta.2"}}, "../../node_modules/.pnpm/@antfu+eslint-config@3.16.0_@typescript-eslint+utils@8.32.1_eslint@9.27.0_jiti@2.4.2__t_352dc122418c7bf09915927e623599ec/node_modules/@antfu/eslint-config": {"version": "3.16.0", "dev": true, "license": "MIT", "dependencies": {"@antfu/install-pkg": "^1.0.0", "@clack/prompts": "^0.9.1", "@eslint-community/eslint-plugin-eslint-comments": "^4.4.1", "@eslint/markdown": "^6.2.1", "@stylistic/eslint-plugin": "^2.13.0", "@typescript-eslint/eslint-plugin": "^8.21.0", "@typescript-eslint/parser": "^8.21.0", "@vitest/eslint-plugin": "^1.1.25", "eslint-config-flat-gitignore": "^1.0.0", "eslint-flat-config-utils": "^1.1.0", "eslint-merge-processors": "^1.0.0", "eslint-plugin-antfu": "^2.7.0", "eslint-plugin-command": "^2.1.0", "eslint-plugin-import-x": "^4.6.1", "eslint-plugin-jsdoc": "^50.6.2", "eslint-plugin-jsonc": "^2.18.2", "eslint-plugin-n": "^17.15.1", "eslint-plugin-no-only-tests": "^3.3.0", "eslint-plugin-perfectionist": "^4.7.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-toml": "^0.12.0", "eslint-plugin-unicorn": "^56.0.1", "eslint-plugin-unused-imports": "^4.1.4", "eslint-plugin-vue": "^9.32.0", "eslint-plugin-yml": "^1.16.0", "eslint-processor-vue-blocks": "^1.0.0", "globals": "^15.14.0", "jsonc-eslint-parser": "^2.4.0", "local-pkg": "^1.0.0", "parse-gitignore": "^2.0.0", "picocolors": "^1.1.1", "toml-eslint-parser": "^0.10.0", "vue-eslint-parser": "^9.4.3", "yaml-eslint-parser": "^1.2.3", "yargs": "^17.7.2"}, "bin": {"eslint-config": "bin/index.js"}, "devDependencies": {"@antfu/eslint-config": "3.16.0", "@antfu/ni": "^23.2.0", "@eslint-react/eslint-plugin": "^1.24.1", "@eslint/config-inspector": "^1.0.0", "@prettier/plugin-xml": "^3.4.1", "@stylistic/eslint-plugin-migrate": "^2.13.0", "@types/fs-extra": "^11.0.4", "@types/node": "^22.10.7", "@types/prompts": "^2.4.9", "@types/yargs": "^17.0.33", "@unocss/eslint-plugin": "^65.4.3", "astro-eslint-parser": "^1.1.0", "bumpp": "^9.10.1", "eslint": "^9.18.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-format": "^1.0.1", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.18", "eslint-plugin-solid": "^0.14.5", "eslint-plugin-svelte": "^2.46.1", "eslint-typegen": "^1.0.0", "execa": "^9.5.2", "fast-glob": "^3.3.3", "fs-extra": "^11.3.0", "jiti": "^2.4.2", "lint-staged": "^15.4.1", "prettier-plugin-astro": "^0.14.1", "prettier-plugin-slidev": "^1.0.5", "rimraf": "^6.0.1", "simple-git-hooks": "^2.11.1", "svelte": "^5.19.1", "svelte-eslint-parser": "^0.43.0", "tsup": "^8.3.5", "tsx": "^4.19.2", "typescript": "^5.7.3", "vitest": "^3.0.3", "vue": "^3.5.13"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@eslint-react/eslint-plugin": "^1.19.0", "@prettier/plugin-xml": "^3.4.1", "@unocss/eslint-plugin": ">=0.50.0", "astro-eslint-parser": "^1.0.2", "eslint": "^9.10.0", "eslint-plugin-astro": "^1.2.0", "eslint-plugin-format": ">=0.1.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.4", "eslint-plugin-solid": "^0.14.3", "eslint-plugin-svelte": ">=2.35.1", "prettier-plugin-astro": "^0.14.0", "prettier-plugin-slidev": "^1.0.5", "svelte-eslint-parser": ">=0.37.0"}, "peerDependenciesMeta": {"@eslint-react/eslint-plugin": {"optional": true}, "@prettier/plugin-xml": {"optional": true}, "@unocss/eslint-plugin": {"optional": true}, "astro-eslint-parser": {"optional": true}, "eslint-plugin-astro": {"optional": true}, "eslint-plugin-format": {"optional": true}, "eslint-plugin-react-hooks": {"optional": true}, "eslint-plugin-react-refresh": {"optional": true}, "eslint-plugin-solid": {"optional": true}, "eslint-plugin-svelte": {"optional": true}, "prettier-plugin-astro": {"optional": true}, "prettier-plugin-slidev": {"optional": true}, "svelte-eslint-parser": {"optional": true}}}, "../../node_modules/.pnpm/@astrojs+solid-js@5.1.0_@types+node@22.15.18_jiti@2.4.2_solid-js@1.9.7_tsx@4.19.4_yaml@2.8.0/node_modules/@astrojs/solid-js": {"version": "5.1.0", "license": "MIT", "dependencies": {"vite": "^6.3.5", "vite-plugin-solid": "^2.11.6"}, "devDependencies": {"astro": "5.8.0", "astro-scripts": "0.0.14", "solid-js": "^1.9.7"}, "engines": {"node": "18.20.8 || ^20.3.0 || >=22.0.0"}, "peerDependencies": {"solid-devtools": "^0.30.1", "solid-js": "^1.8.5"}, "peerDependenciesMeta": {"solid-devtools": {"optional": true}}}, "../../node_modules/.pnpm/@astrojs+starlight@0.34.3_astro@5.8.0_@azure+storage-blob@12.27.0_@types+node@22.15.18__4d4f74c0c66fb81f0333178c09858387/node_modules/@astrojs/starlight": {"version": "0.34.3", "license": "MIT", "dependencies": {"@astrojs/markdown-remark": "^6.3.1", "@astrojs/mdx": "^4.2.3", "@astrojs/sitemap": "^3.3.0", "@pagefind/default-ui": "^1.3.0", "@types/hast": "^3.0.4", "@types/js-yaml": "^4.0.9", "@types/mdast": "^4.0.4", "astro-expressive-code": "^0.41.1", "bcp-47": "^2.1.0", "hast-util-from-html": "^2.0.1", "hast-util-select": "^6.0.2", "hast-util-to-string": "^3.0.0", "hastscript": "^9.0.0", "i18next": "^23.11.5", "js-yaml": "^4.1.0", "klona": "^2.0.6", "mdast-util-directive": "^3.0.0", "mdast-util-to-markdown": "^2.1.0", "mdast-util-to-string": "^4.0.0", "pagefind": "^1.3.0", "rehype": "^13.0.1", "rehype-format": "^5.0.0", "remark-directive": "^3.0.0", "ultrahtml": "^1.6.0", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "vfile": "^6.0.2"}, "devDependencies": {"@playwright/test": "^1.45.0", "@types/node": "^18.16.19", "@vitest/coverage-v8": "^3.0.5", "astro": "^5.6.1", "linkedom": "^0.18.4", "vitest": "^3.0.5"}, "peerDependencies": {"astro": "^5.5.0"}}, "../../node_modules/.pnpm/@iconify-json+tabler@1.2.18/node_modules/@iconify-json/tabler": {"version": "1.2.18", "dev": true, "license": "MIT", "dependencies": {"@iconify/types": "*"}}, "../../node_modules/.pnpm/@types+lodash-es@4.17.12/node_modules/@types/lodash-es": {"version": "4.17.12", "dev": true, "license": "MIT", "dependencies": {"@types/lodash": "*"}}, "../../node_modules/.pnpm/@unocss+reset@0.64.1/node_modules/@unocss/reset": {"version": "0.64.1", "dev": true, "license": "MIT", "devDependencies": {"@csstools/normalize.css": "^12.1.1", "sanitize.css": "^13.0.0"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "../../node_modules/.pnpm/astro@5.8.0_@azure+storage-blob@12.27.0_@types+node@22.15.18_idb-keyval@6.2.1_jiti@2.4._36113cc1ee1815329834e427fd88cdcd/node_modules/astro": {"version": "5.8.0", "license": "MIT", "dependencies": {"@astrojs/compiler": "^2.11.0", "@astrojs/internal-helpers": "0.6.1", "@astrojs/markdown-remark": "6.3.2", "@astrojs/telemetry": "3.3.0", "@capsizecss/unpack": "^2.4.0", "@oslojs/encoding": "^1.1.0", "@rollup/pluginutils": "^5.1.4", "acorn": "^8.14.1", "aria-query": "^5.3.2", "axobject-query": "^4.1.0", "boxen": "8.0.1", "ci-info": "^4.2.0", "clsx": "^2.1.1", "common-ancestor-path": "^1.0.1", "cookie": "^1.0.2", "cssesc": "^3.0.0", "debug": "^4.4.0", "deterministic-object-hash": "^2.0.2", "devalue": "^5.1.1", "diff": "^5.2.0", "dlv": "^1.1.3", "dset": "^3.1.4", "es-module-lexer": "^1.6.0", "esbuild": "^0.25.0", "estree-walker": "^3.0.3", "flattie": "^1.1.1", "fontace": "~0.3.0", "github-slugger": "^2.0.0", "html-escaper": "3.0.3", "http-cache-semantics": "^4.1.1", "import-meta-resolve": "^4.1.0", "js-yaml": "^4.1.0", "kleur": "^4.1.5", "magic-string": "^0.30.17", "magicast": "^0.3.5", "mrmime": "^2.0.1", "neotraverse": "^0.6.18", "p-limit": "^6.2.0", "p-queue": "^8.1.0", "package-manager-detector": "^1.1.0", "picomatch": "^4.0.2", "prompts": "^2.4.2", "rehype": "^13.0.2", "semver": "^7.7.1", "shiki": "^3.2.1", "tinyexec": "^0.3.2", "tinyglobby": "^0.2.12", "tsconfck": "^3.1.5", "ultrahtml": "^1.6.0", "unifont": "~0.5.0", "unist-util-visit": "^5.0.0", "unstorage": "^1.15.0", "vfile": "^6.0.3", "vite": "^6.3.4", "vitefu": "^1.0.6", "xxhash-wasm": "^1.1.0", "yargs-parser": "^21.1.1", "yocto-spinner": "^0.2.1", "zod": "^3.24.2", "zod-to-json-schema": "^3.24.5", "zod-to-ts": "^1.2.0"}, "bin": {"astro": "astro.js"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@playwright/test": "^1.51.1", "@types/aria-query": "^5.0.4", "@types/common-ancestor-path": "^1.0.2", "@types/cssesc": "^3.0.2", "@types/debug": "^4.1.12", "@types/diff": "^5.2.3", "@types/dlv": "^1.1.5", "@types/hast": "^3.0.4", "@types/html-escaper": "3.0.4", "@types/http-cache-semantics": "^4.0.4", "@types/js-yaml": "^4.0.9", "@types/picomatch": "^3.0.2", "@types/prompts": "^2.4.9", "@types/semver": "^7.7.0", "@types/yargs-parser": "^21.0.3", "astro-scripts": "0.0.14", "cheerio": "1.0.0", "eol": "^0.10.0", "execa": "^8.0.1", "expect-type": "^1.2.0", "fs-fixture": "^2.7.1", "mdast-util-mdx": "^3.0.0", "mdast-util-mdx-jsx": "^3.2.0", "node-mocks-http": "^1.16.2", "parse-srcset": "^1.0.2", "rehype-autolink-headings": "^7.1.0", "rehype-slug": "^6.0.0", "rehype-toc": "^3.0.2", "remark-code-titles": "^0.1.2", "rollup": "^4.37.0", "sass": "^1.86.0", "typescript": "^5.8.3", "undici": "^7.5.0", "unified": "^11.0.5", "vitest": "^3.0.9"}, "engines": {"node": "18.20.8 || ^20.3.0 || >=22.0.0", "npm": ">=9.6.5", "pnpm": ">=7.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/astrodotbuild"}, "optionalDependencies": {"sharp": "^0.33.3"}}, "../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority": {"version": "0.7.1", "license": "Apache-2.0", "dependencies": {"clsx": "^2.1.1"}, "devDependencies": {"@swc/cli": "0.3.12", "@swc/core": "1.4.16", "@types/node": "20.12.7", "@types/react": "18.2.79", "@types/react-dom": "18.2.25", "bundlesize": "0.18.2", "npm-run-all": "4.1.5", "react": "18.2.0", "react-dom": "18.2.0", "ts-node": "10.9.2", "typescript": "5.4.5"}, "funding": {"url": "https://polar.sh/cva"}}, "../../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx": {"version": "2.1.1", "license": "MIT", "devDependencies": {"esm": "3.2.25", "terser": "4.8.0", "uvu": "0.5.4"}, "engines": {"node": ">=6"}}, "../../node_modules/.pnpm/eslint-plugin-astro@1.3.1_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3/node_modules/eslint-plugin-astro": {"version": "1.3.1", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@jridgewell/sourcemap-codec": "^1.4.14", "@typescript-eslint/types": "^7.7.1 || ^8", "astro-eslint-parser": "^1.0.2", "eslint-compat-utils": "^0.6.0", "globals": "^15.0.0", "postcss": "^8.4.14", "postcss-selector-parser": "^7.0.0"}, "devDependencies": {"@astrojs/compiler": "^2.0.0", "@astrojs/mdx": "^3.0.0", "@astrojs/svelte": "^5.0.0", "@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.24.2", "@eslint-community/eslint-plugin-eslint-comments": "^4.3.0", "@eslint/eslintrc": "^3.0.0", "@eslint/js": "^9.0.0", "@ota-meshi/eslint-plugin": "^0.17.0", "@types/eslint": "^9.0.0", "@types/eslint-scope": "^3.7.3", "@types/eslint-utils": "^3.0.1", "@types/estree": "1.0.6", "@types/less": "^3.0.3", "@types/mocha": "^10.0.0", "@types/node": "^22.0.0", "@types/react": "^18.0.15", "@types/semver": "^7.3.9", "@types/stylus": "^0.48.38", "@typescript-eslint/eslint-plugin": "^8.5.0", "@typescript-eslint/parser": "^8.5.0", "assert": "^2.0.0", "astro": "^4.5.0", "env-cmd": "^10.1.0", "esbuild": "^0.24.0", "esbuild-register": "^3.3.3", "eslint": "^9.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-astro": "^1.2.3", "eslint-plugin-eslint-plugin": "^6.0.0", "eslint-plugin-jsdoc": "^50.0.0", "eslint-plugin-json-schema-validator": "^5.0.0", "eslint-plugin-jsonc": "^2.15.1", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-markdown": "^5.1.0", "eslint-plugin-n": "^17.2.0", "eslint-plugin-node-dependencies": "^0.12.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-regexp": "^2.5.0", "eslint-plugin-svelte": "^2.36.0", "eslint-plugin-yml": "^1.0.0", "expect-type": "^1.0.0", "js-yaml": "^4.1.0", "less": "^4.1.2", "mocha": "^10.0.0", "monaco-editor": "^0.52.0", "nyc": "^17.0.0", "pako": "^2.0.4", "postcss-nested": "^7.0.0", "prettier": "^3.0.0", "prettier-plugin-astro": "^0.14.0", "prettier-plugin-svelte": "^3.0.0", "remark-gfm": "^4.0.0", "rimraf": "^6.0.0", "sass": "^1.52.2", "stylus": "^0.64.0", "svelte": "^4.0.0", "tsup": "^8.0.2", "typescript": "~5.6.3", "typescript-eslint": "^8.5.0", "vite-plugin-eslint4b": "^0.4.6"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://github.com/sponsors/ota-meshi"}, "peerDependencies": {"eslint": ">=8.57.0"}}, "../../node_modules/.pnpm/eslint@9.27.0_jiti@2.4.2/node_modules/eslint": {"version": "9.27.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.12.1", "@eslint/config-array": "^0.20.0", "@eslint/config-helpers": "^0.2.1", "@eslint/core": "^0.14.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.27.0", "@eslint/plugin-kit": "^0.3.1", "@humanfs/node": "^0.16.6", "@humanwhocodes/module-importer": "^1.0.1", "@humanwhocodes/retry": "^0.4.2", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.6", "debug": "^4.3.2", "escape-string-regexp": "^4.0.0", "eslint-scope": "^8.3.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.5.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "json-stable-stringify-without-jsonify": "^1.0.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3"}, "bin": {"eslint": "bin/eslint.js"}, "devDependencies": {"@arethetypeswrong/cli": "^0.18.0", "@babel/core": "^7.4.3", "@babel/preset-env": "^7.4.3", "@cypress/webpack-preprocessor": "^6.0.2", "@eslint/json": "^0.12.0", "@trunkio/launcher": "^1.3.4", "@types/esquery": "^1.5.4", "@types/node": "^22.13.14", "@typescript-eslint/parser": "^8.4.0", "babel-loader": "^8.0.5", "c8": "^7.12.0", "chai": "^4.0.1", "cheerio": "^0.22.0", "common-tags": "^1.8.0", "core-js": "^3.1.3", "cypress": "^14.1.0", "ejs": "^3.0.2", "eslint": "file:.", "eslint-config-eslint": "file:packages/eslint-config-eslint", "eslint-plugin-eslint-plugin": "^6.0.0", "eslint-plugin-expect-type": "^0.6.0", "eslint-plugin-yml": "^1.14.0", "eslint-release": "^3.3.0", "eslint-rule-composer": "^0.3.0", "eslump": "^3.0.0", "esprima": "^4.0.1", "fast-glob": "^3.2.11", "fs-teardown": "^0.1.3", "glob": "^10.0.0", "globals": "^15.0.0", "got": "^11.8.3", "gray-matter": "^4.0.3", "jiti": "^2.1.0", "knip": "^5.32.0", "lint-staged": "^11.0.0", "load-perf": "^0.2.0", "markdown-it": "^12.2.0", "markdown-it-container": "^3.0.0", "marked": "^4.0.8", "metascraper": "^5.25.7", "metascraper-description": "^5.25.7", "metascraper-image": "^5.29.3", "metascraper-logo": "^5.25.7", "metascraper-logo-favicon": "^5.25.7", "metascraper-title": "^5.25.7", "mocha": "^10.7.3", "node-polyfill-webpack-plugin": "^1.0.3", "npm-license": "^0.3.3", "pirates": "^4.0.5", "progress": "^2.0.3", "proxyquire": "^2.0.1", "recast": "^0.23.0", "regenerator-runtime": "^0.14.0", "semver": "^7.5.3", "shelljs": "^0.9.0", "sinon": "^11.0.0", "typescript": "^5.3.3", "webpack": "^5.23.0", "webpack-cli": "^4.5.0", "yorkie": "^2.0.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}, "peerDependencies": {"jiti": "*"}, "peerDependenciesMeta": {"jiti": {"optional": true}}}, "../../node_modules/.pnpm/figue@2.2.3_zod@3.24.4/node_modules/figue": {"version": "2.2.3", "dev": true, "license": "MIT", "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@antfu/eslint-config": "^3.5.0", "bumpp": "^9.5.2", "eslint": "^9.10.0", "standard-version": "^9.5.0", "typescript": "^5.4.5", "unbuild": "^2.0.0", "vitest": "^1.6.0"}, "funding": {"url": "https://github.com/sponsors/CorentinTh"}, "peerDependencies": {"zod": "^3.22.4"}}, "../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es": {"version": "4.17.21", "dev": true, "license": "MIT"}, "../../node_modules/.pnpm/marked@15.0.11/node_modules/marked": {"version": "15.0.11", "dev": true, "license": "MIT", "bin": {"marked": "bin/marked.js"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.4", "@markedjs/eslint-config": "^1.0.12", "@markedjs/testutils": "14.1.1-0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.1", "@semantic-release/npm": "^12.0.1", "@semantic-release/release-notes-generator": "^14.0.3", "cheerio": "1.0.0", "commonmark": "0.31.2", "cross-env": "^7.0.3", "dts-bundle-generator": "^9.5.1", "eslint": "^9.25.0", "highlight.js": "^11.11.1", "markdown-it": "14.1.0", "marked-highlight": "^2.2.1", "marked-man": "^2.1.0", "recheck": "^4.5.0", "rollup": "^4.40.0", "semantic-release": "^24.2.3", "titleize": "^4.0.0", "tslib": "^2.8.1", "typescript": "5.8.3"}, "engines": {"node": ">= 18"}}, "../../node_modules/.pnpm/sharp@0.32.6/node_modules/sharp": {"version": "0.32.6", "hasInstallScript": true, "license": "Apache-2.0", "dependencies": {"color": "^4.2.3", "detect-libc": "^2.0.2", "node-addon-api": "^6.1.0", "prebuild-install": "^7.1.1", "semver": "^7.5.4", "simple-get": "^4.0.1", "tar-fs": "^3.0.4", "tunnel-agent": "^0.6.0"}, "devDependencies": {"@types/node": "*", "async": "^3.2.4", "cc": "^3.0.1", "exif-reader": "^1.2.0", "extract-zip": "^2.0.1", "icc": "^3.0.0", "jsdoc-to-markdown": "^8.0.0", "license-checker": "^25.0.1", "mocha": "^10.2.0", "mock-fs": "^5.2.0", "nyc": "^15.1.0", "prebuild": "^12.0.0", "semistandard": "^16.0.1", "tsd": "^0.29.0"}, "engines": {"node": ">=14.15.0"}, "funding": {"url": "https://opencollective.com/libvips"}}, "../../node_modules/.pnpm/shiki@3.4.2/node_modules/shiki": {"version": "3.4.2", "license": "MIT", "dependencies": {"@shikijs/core": "3.4.2", "@shikijs/engine-javascript": "3.4.2", "@shikijs/engine-oniguruma": "3.4.2", "@shikijs/langs": "3.4.2", "@shikijs/themes": "3.4.2", "@shikijs/types": "3.4.2", "@shikijs/vscode-textmate": "^10.0.2", "@types/hast": "^3.0.4"}, "devDependencies": {"rollup-plugin-copy": "^3.5.0", "tm-grammars": "^1.23.20", "tm-themes": "^1.10.6", "vscode-oniguruma": "1.7.0"}}, "../../node_modules/.pnpm/solid-js@1.9.7/node_modules/solid-js": {"version": "1.9.7", "license": "MIT", "dependencies": {"csstype": "^3.1.0", "seroval": "~1.3.0", "seroval-plugins": "~1.3.0"}}, "../../node_modules/.pnpm/starlight-links-validator@0.16.0_@astrojs+starlight@0.34.3_astro@5.8.0_@azure+storage-b_cb092014de3e305f7f9a2901f248f83e/node_modules/starlight-links-validator": {"version": "0.16.0", "license": "MIT", "dependencies": {"@types/picomatch": "^3.0.1", "github-slugger": "^2.0.0", "hast-util-from-html": "^2.0.3", "hast-util-has-property": "^3.0.0", "is-absolute-url": "^4.0.1", "kleur": "^4.1.5", "mdast-util-mdx-jsx": "^3.1.3", "mdast-util-to-string": "^4.0.0", "picomatch": "^4.0.2", "unist-util-visit": "^5.0.0"}, "devDependencies": {"@types/hast": "^3.0.4", "@types/mdast": "^4.0.4", "@types/node": "^18.19.68", "remark-custom-heading-id": "^2.0.0", "unified": "^11.0.5", "vfile": "^6.0.3", "vitest": "2.1.6"}, "engines": {"node": ">=18.17.1"}, "peerDependencies": {"@astrojs/starlight": ">=0.32.0"}}, "../../node_modules/.pnpm/starlight-theme-rapide@0.5.1_@astrojs+starlight@0.34.3_astro@5.8.0_@azure+storage-blob@_5f544216a4e1ec7b2f6d8216c5c195ff/node_modules/starlight-theme-rapide": {"version": "0.5.1", "license": "MIT", "engines": {"node": ">=18"}, "peerDependencies": {"@astrojs/starlight": ">=0.34.0"}}, "../../node_modules/.pnpm/tailwind-merge@2.6.0/node_modules/tailwind-merge": {"version": "2.6.0", "license": "MIT", "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@codspeed/vitest-plugin": "^4.0.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-typescript": "^12.1.1", "@vitest/coverage-v8": "^2.1.8", "@vitest/eslint-plugin": "^1.1.14", "babel-plugin-annotate-pure-calls": "^0.4.0", "babel-plugin-polyfill-regenerator": "^0.6.3", "eslint": "^9.16.0", "eslint-plugin-import": "^2.31.0", "globby": "^11.1.0", "prettier": "^3.4.2", "rollup": "^4.28.1", "rollup-plugin-delete": "^2.1.0", "rollup-plugin-dts": "^6.1.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.17.0", "vitest": "^2.1.8", "zx": "^8.2.4"}, "funding": {"type": "github", "url": "https://github.com/sponsors/dcastil"}}, "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript": {"version": "5.8.3", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "devDependencies": {"@dprint/formatter": "^0.4.1", "@dprint/typescript": "0.93.3", "@esfx/canceltoken": "^1.0.0", "@eslint/js": "^9.17.0", "@octokit/rest": "^21.0.2", "@types/chai": "^4.3.20", "@types/diff": "^5.2.3", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.10", "@types/ms": "^0.7.34", "@types/node": "latest", "@types/source-map-support": "^0.5.10", "@types/which": "^3.0.4", "@typescript-eslint/rule-tester": "^8.18.1", "@typescript-eslint/type-utils": "^8.18.1", "@typescript-eslint/utils": "^8.18.1", "azure-devops-node-api": "^14.1.0", "c8": "^10.1.3", "chai": "^4.5.0", "chalk": "^4.1.2", "chokidar": "^3.6.0", "diff": "^5.2.0", "dprint": "^0.47.6", "esbuild": "^0.24.0", "eslint": "^9.17.0", "eslint-formatter-autolinkable-stylish": "^1.4.0", "eslint-plugin-regexp": "^2.7.0", "fast-xml-parser": "^4.5.1", "glob": "^10.4.5", "globals": "^15.13.0", "hereby": "^1.10.0", "jsonc-parser": "^3.3.1", "knip": "^5.41.0", "minimist": "^1.2.8", "mocha": "^10.8.2", "mocha-fivemat-progress-reporter": "^0.1.0", "monocart-coverage-reports": "^2.11.4", "ms": "^2.1.3", "playwright": "^1.49.1", "source-map-support": "^0.5.21", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1", "which": "^3.0.1"}, "engines": {"node": ">=14.17"}}, "../../node_modules/.pnpm/unocss-preset-animations@1.2.1_unocss@0.65.0-beta.2_postcss@8.5.3_rollup@4.39.0_vite@6._5ff74c8d4816a6009793de99eed33b3b/node_modules/unocss-preset-animations": {"version": "1.2.1", "license": "MIT", "devDependencies": {"@aelita-dev/eslint-config": "3.19.0", "@iconify/json": "^2.2.330", "@types/dom-view-transitions": "^1.0.6", "@types/markdown-it": "^14.1.2", "@types/node": "^20.17.30", "@unocss/core": "^66.0.0", "@unocss/eslint-plugin": "^66.0.0", "@unocss/preset-mini": "^66.0.0", "@vitest/coverage-v8": "^3.1.2", "@vitest/eslint-plugin": "^1.1.43", "@vue/language-server": "^2.2.10", "bumpp": "^10.1.0", "bundle-require": "^5.1.0", "changelogithub": "^13.13.0", "eslint": "^9.25.1", "eslint-import-resolver-typescript": "^4.3.4", "eslint-plugin-import-x": "^4.10.6", "eslint-plugin-vue": "^10.0.0", "eslint-plugin-vuejs-accessibility": "^2.4.1", "eslint-processor-vue-blocks": "^2.0.0", "lint-staged": "^15.5.1", "markdown-it": "^14.1.0", "sass-embedded": "^1.87.0", "simple-git-hooks": "^2.12.1", "typescript": "~5.8.3", "unbuild": "3.5.0", "unocss": "^66.0.0", "vite-tsconfig-paths": "^5.1.4", "vitepress": "1.6.3", "vitest": "^3.1.2", "vue": "^3.5.13", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^2.2.10"}, "peerDependencies": {"@unocss/preset-wind3": ">=0.56.0 < 101", "unocss": ">=0.56.0 < 101"}, "peerDependenciesMeta": {"@unocss/preset-wind3": {"optional": true}}}, "../../node_modules/.pnpm/unocss@0.65.0-beta.2_postcss@8.5.3_rollup@4.39.0_vite@6.3.4_@types+node@22.15.18_jiti@2_21784306d3aa16d487b93eb21b3d7dd5/node_modules/unocss": {"version": "0.65.0-beta.2", "dev": true, "license": "MIT", "dependencies": {"@unocss/astro": "0.65.0-beta.2", "@unocss/cli": "0.65.0-beta.2", "@unocss/core": "0.65.0-beta.2", "@unocss/postcss": "0.65.0-beta.2", "@unocss/preset-attributify": "0.65.0-beta.2", "@unocss/preset-icons": "0.65.0-beta.2", "@unocss/preset-mini": "0.65.0-beta.2", "@unocss/preset-tagify": "0.65.0-beta.2", "@unocss/preset-typography": "0.65.0-beta.2", "@unocss/preset-uno": "0.65.0-beta.2", "@unocss/preset-web-fonts": "0.65.0-beta.2", "@unocss/preset-wind": "0.65.0-beta.2", "@unocss/transformer-attributify-jsx": "0.65.0-beta.2", "@unocss/transformer-compile-class": "0.65.0-beta.2", "@unocss/transformer-directives": "0.65.0-beta.2", "@unocss/transformer-variant-group": "0.65.0-beta.2", "@unocss/vite": "0.65.0-beta.2"}, "devDependencies": {"@unocss/webpack": "0.65.0-beta.2", "vite": "^6.0.0"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@unocss/webpack": "0.65.0-beta.2", "vite": "^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0"}, "peerDependenciesMeta": {"@unocss/webpack": {"optional": true}, "vite": {"optional": true}}}, "../../node_modules/.pnpm/zod-to-json-schema@3.24.5_zod@3.24.4/node_modules/zod-to-json-schema": {"version": "3.24.5", "license": "ISC", "devDependencies": {"@types/json-schema": "^7.0.9", "@types/node": "^20.9.0", "ajv": "^8.6.3", "ajv-errors": "^3.0.0", "ajv-formats": "^2.1.1", "fast-diff": "^1.3.0", "local-ref-resolver": "^0.2.0", "rimraf": "^3.0.2", "tsx": "^4.19.0", "typescript": "^5.1.3", "zod": "^3.24.1"}, "peerDependencies": {"zod": "^3.24.1"}}, "node_modules/@antfu/eslint-config": {"resolved": "../../node_modules/.pnpm/@antfu+eslint-config@3.16.0_@typescript-eslint+utils@8.32.1_eslint@9.27.0_jiti@2.4.2__t_352dc122418c7bf09915927e623599ec/node_modules/@antfu/eslint-config", "link": true}, "node_modules/@astrojs/solid-js": {"resolved": "../../node_modules/.pnpm/@astrojs+solid-js@5.1.0_@types+node@22.15.18_jiti@2.4.2_solid-js@1.9.7_tsx@4.19.4_yaml@2.8.0/node_modules/@astrojs/solid-js", "link": true}, "node_modules/@astrojs/starlight": {"resolved": "../../node_modules/.pnpm/@astrojs+starlight@0.34.3_astro@5.8.0_@azure+storage-blob@12.27.0_@types+node@22.15.18__4d4f74c0c66fb81f0333178c09858387/node_modules/@astrojs/starlight", "link": true}, "node_modules/@corvu/utils": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/@corvu/utils/-/utils-0.4.2.tgz", "integrity": "sha512-Ox2kYyxy7NoXdKWdHeDEjZxClwzO4SKM8plAaVwmAJPxHMqA0rLOoAsa+hBDwRLpctf+ZRnAd/ykguuJidnaTA==", "license": "MIT", "dependencies": {"@floating-ui/dom": "^1.6.11"}, "peerDependencies": {"solid-js": "^1.8"}}, "node_modules/@floating-ui/core": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/@floating-ui/core/-/core-1.7.0.tgz", "integrity": "sha512-FRdBLykrPPA6P76GGGqlex/e7fbe0F1ykgxHYNXQsH/iTEtjMj/f9bpY5oQqbjt5VgZvgz/uKXbGuROijh3VLA==", "license": "MIT", "dependencies": {"@floating-ui/utils": "^0.2.9"}}, "node_modules/@floating-ui/dom": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.7.0.tgz", "integrity": "sha512-lG<PERSON>or4VlXcesUMh1cupTUTDoCxMb0V6bm3CnxHzQcw8Eaf1jQbgQX4i02fYgT0vJ82tb5MZ4CZk1LRGkktJCzg==", "license": "MIT", "dependencies": {"@floating-ui/core": "^1.7.0", "@floating-ui/utils": "^0.2.9"}}, "node_modules/@floating-ui/utils": {"version": "0.2.9", "resolved": "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.9.tgz", "integrity": "sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==", "license": "MIT"}, "node_modules/@iconify-json/tabler": {"resolved": "../../node_modules/.pnpm/@iconify-json+tabler@1.2.18/node_modules/@iconify-json/tabler", "link": true}, "node_modules/@internationalized/date": {"version": "3.8.1", "resolved": "https://registry.npmjs.org/@internationalized/date/-/date-3.8.1.tgz", "integrity": "sha512-PgVE6B6eIZtzf9Gu5HvJxRK3ufUFz9DhspELuhW/N0GuMGMTLvPQNRkHP2hTuP9lblOk+f+1xi96sPiPXANXAA==", "license": "Apache-2.0", "dependencies": {"@swc/helpers": "^0.5.0"}}, "node_modules/@internationalized/number": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/@internationalized/number/-/number-3.6.2.tgz", "integrity": "sha512-E5QTOlMg9wo5OrKdHD6edo1JJlIoOsylh0+mbf0evi1tHJwMZfJSaBpGtnJV9N7w3jeiioox9EG/EWRWPh82vg==", "license": "Apache-2.0", "dependencies": {"@swc/helpers": "^0.5.0"}}, "node_modules/@kobalte/core": {"version": "0.13.9", "resolved": "https://registry.npmjs.org/@kobalte/core/-/core-0.13.9.tgz", "integrity": "sha512-TkeSpgNy7I5k8jwjqT9CK3teAxN0aFb3yyL9ODb06JVYMwXIk+UKrizoAF1ahLUP85lKnxv44B4Y5cXkHShgqw==", "license": "MIT", "dependencies": {"@floating-ui/dom": "^1.5.1", "@internationalized/date": "^3.4.0", "@internationalized/number": "^3.2.1", "@kobalte/utils": "^0.9.1", "@solid-primitives/props": "^3.1.8", "@solid-primitives/resize-observer": "^2.0.26", "solid-presence": "^0.1.8", "solid-prevent-scroll": "^0.1.4"}, "peerDependencies": {"solid-js": "^1.8.15"}}, "node_modules/@kobalte/utils": {"version": "0.9.1", "resolved": "https://registry.npmjs.org/@kobalte/utils/-/utils-0.9.1.tgz", "integrity": "sha512-eeU60A3kprIiBDAfv9gUJX1tXGLuZiKMajUfSQURAF2pk4ZoMYiqIzmrMBvzcxP39xnYttgTyQEVLwiTZnrV4w==", "license": "MIT", "dependencies": {"@solid-primitives/event-listener": "^2.2.14", "@solid-primitives/keyed": "^1.2.0", "@solid-primitives/map": "^0.4.7", "@solid-primitives/media": "^2.2.4", "@solid-primitives/props": "^3.1.8", "@solid-primitives/refs": "^1.0.5", "@solid-primitives/utils": "^6.2.1"}, "peerDependencies": {"solid-js": "^1.8.8"}}, "node_modules/@solid-primitives/event-listener": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@solid-primitives/event-listener/-/event-listener-2.4.1.tgz", "integrity": "sha512-Xc/lBCeuh9LwzR4lYbMDtopwWK7N9b4o+FmI4uoI8DOtVGYi0Ip20DG8PtwHk+g31lHgvwtFFVKfnUx2UaqZJg==", "license": "MIT", "dependencies": {"@solid-primitives/utils": "^6.3.1"}, "peerDependencies": {"solid-js": "^1.6.12"}}, "node_modules/@solid-primitives/keyed": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/@solid-primitives/keyed/-/keyed-1.5.1.tgz", "integrity": "sha512-lAgQ1Jou8nxywifWsWjDTla9MI7Pfr46JRTC40K81fqz8dw4E8t/4gYuIwqP1EHVG0mItfIb3XqDw0wEQh+QYA==", "license": "MIT", "peerDependencies": {"solid-js": "^1.6.12"}}, "node_modules/@solid-primitives/map": {"version": "0.4.13", "resolved": "https://registry.npmjs.org/@solid-primitives/map/-/map-0.4.13.tgz", "integrity": "sha512-B1zyFbsiTQvqPr+cuPCXO72sRuczG9Swncqk5P74NCGw1VE8qa/Ry9GlfI1e/VdeQYHjan+XkbE3rO2GW/qKew==", "license": "MIT", "dependencies": {"@solid-primitives/trigger": "^1.1.0"}, "peerDependencies": {"solid-js": "^1.6.12"}}, "node_modules/@solid-primitives/media": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/@solid-primitives/media/-/media-2.3.1.tgz", "integrity": "sha512-UTX8LAaQS7k3rvekme8y5ihOrt5SJpgkw7xyUySlPhIapD7JxlhYncQoSFsys5D1XPCgI/3snobpvbanRcrTAw==", "license": "MIT", "dependencies": {"@solid-primitives/event-listener": "^2.4.1", "@solid-primitives/rootless": "^1.5.1", "@solid-primitives/static-store": "^0.1.1", "@solid-primitives/utils": "^6.3.1"}, "peerDependencies": {"solid-js": "^1.6.12"}}, "node_modules/@solid-primitives/props": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/@solid-primitives/props/-/props-3.2.1.tgz", "integrity": "sha512-SuTuCctLLZbUL1QyWamQGWSWPIgoc/gXt5kL8P2yLhb51f9Dj+WHxU0shXBjzx7z+hDc5KtheQgM4NnJqQJi2A==", "license": "MIT", "dependencies": {"@solid-primitives/utils": "^6.3.1"}, "peerDependencies": {"solid-js": "^1.6.12"}}, "node_modules/@solid-primitives/refs": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@solid-primitives/refs/-/refs-1.1.1.tgz", "integrity": "sha512-MIQ7Bh59IiT9NDQPf6iWRnPe0RgKggEjF0H+iMoIi1KBCcp4Mfss2IkUWYPr9wqQg963ZQFbcg5D6oN9Up6Mww==", "license": "MIT", "dependencies": {"@solid-primitives/utils": "^6.3.1"}, "peerDependencies": {"solid-js": "^1.6.12"}}, "node_modules/@solid-primitives/resize-observer": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/@solid-primitives/resize-observer/-/resize-observer-2.1.1.tgz", "integrity": "sha512-vb/VS9+YdUdVZ2V92JimFmFuaJ2MSyKOGnUay/mQvoQ0R+mtdT7FSylfQlVslCzm0ecx8Jkvsm1Sk2lopvMAdg==", "license": "MIT", "dependencies": {"@solid-primitives/event-listener": "^2.4.1", "@solid-primitives/rootless": "^1.5.1", "@solid-primitives/static-store": "^0.1.1", "@solid-primitives/utils": "^6.3.1"}, "peerDependencies": {"solid-js": "^1.6.12"}}, "node_modules/@solid-primitives/rootless": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/@solid-primitives/rootless/-/rootless-1.5.1.tgz", "integrity": "sha512-G4eNC6F3ufRT2Mjbodl7rSOH7uq/Emqs3S7/BIBWgh+V/IFUtvu6WELeqSrk4FJX3T/kKKvC+T8gXhepExSWyg==", "license": "MIT", "dependencies": {"@solid-primitives/utils": "^6.3.1"}, "peerDependencies": {"solid-js": "^1.6.12"}}, "node_modules/@solid-primitives/static-store": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/@solid-primitives/static-store/-/static-store-0.1.1.tgz", "integrity": "sha512-daXWvpLjd+4hbYdGaaEJ2kKFuFhshvfIBFLveW7mfk2BWHl9lGQVwUuExp3qllkK9ONA9p+5D2cpwBQosv8odQ==", "license": "MIT", "dependencies": {"@solid-primitives/utils": "^6.3.1"}, "peerDependencies": {"solid-js": "^1.6.12"}}, "node_modules/@solid-primitives/trigger": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@solid-primitives/trigger/-/trigger-1.2.1.tgz", "integrity": "sha512-pvNmddYs5AYUpiH373F7wbQOlcc10SSNHY8kUiu4UHoDlv4jhSnlNXzbFkmt33hq4ODKdN5gVm00jCnAJ+wm8Q==", "license": "MIT", "dependencies": {"@solid-primitives/utils": "^6.3.1"}, "peerDependencies": {"solid-js": "^1.6.12"}}, "node_modules/@solid-primitives/utils": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/@solid-primitives/utils/-/utils-6.3.1.tgz", "integrity": "sha512-4/Z59nnwu4MPR//zWZmZm2yftx24jMqQ8CSd/JobL26TPfbn4Ph8GKNVJfGJWShg1QB98qObJSskqizbTvcLLA==", "license": "MIT", "peerDependencies": {"solid-js": "^1.6.12"}}, "node_modules/@swc/helpers": {"version": "0.5.17", "resolved": "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.17.tgz", "integrity": "sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==", "license": "Apache-2.0", "dependencies": {"tslib": "^2.8.0"}}, "node_modules/@types/lodash-es": {"resolved": "../../node_modules/.pnpm/@types+lodash-es@4.17.12/node_modules/@types/lodash-es", "link": true}, "node_modules/@unocss/reset": {"resolved": "../../node_modules/.pnpm/@unocss+reset@0.64.1/node_modules/@unocss/reset", "link": true}, "node_modules/astro": {"resolved": "../../node_modules/.pnpm/astro@5.8.0_@azure+storage-blob@12.27.0_@types+node@22.15.18_idb-keyval@6.2.1_jiti@2.4._36113cc1ee1815329834e427fd88cdcd/node_modules/astro", "link": true}, "node_modules/class-variance-authority": {"resolved": "../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority", "link": true}, "node_modules/clsx": {"resolved": "../../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx", "link": true}, "node_modules/eslint": {"resolved": "../../node_modules/.pnpm/eslint@9.27.0_jiti@2.4.2/node_modules/eslint", "link": true}, "node_modules/eslint-plugin-astro": {"resolved": "../../node_modules/.pnpm/eslint-plugin-astro@1.3.1_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3/node_modules/eslint-plugin-astro", "link": true}, "node_modules/figue": {"resolved": "../../node_modules/.pnpm/figue@2.2.3_zod@3.24.4/node_modules/figue", "link": true}, "node_modules/lodash-es": {"resolved": "../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es", "link": true}, "node_modules/marked": {"resolved": "../../node_modules/.pnpm/marked@15.0.11/node_modules/marked", "link": true}, "node_modules/sharp": {"resolved": "../../node_modules/.pnpm/sharp@0.32.6/node_modules/sharp", "link": true}, "node_modules/shiki": {"resolved": "../../node_modules/.pnpm/shiki@3.4.2/node_modules/shiki", "link": true}, "node_modules/solid-js": {"resolved": "../../node_modules/.pnpm/solid-js@1.9.7/node_modules/solid-js", "link": true}, "node_modules/solid-presence": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/solid-presence/-/solid-presence-0.1.8.tgz", "integrity": "sha512-pWGtXUFWYYUZNbg5YpG5vkQJyOtzn2KXhxYaMx/4I+lylTLYkITOLevaCwMRN+liCVk0pqB6EayLWojNqBFECA==", "license": "MIT", "dependencies": {"@corvu/utils": "~0.4.0"}, "peerDependencies": {"solid-js": "^1.8"}}, "node_modules/solid-prevent-scroll": {"version": "0.1.10", "resolved": "https://registry.npmjs.org/solid-prevent-scroll/-/solid-prevent-scroll-0.1.10.tgz", "integrity": "sha512-KplGPX2GHiWJLZ6AXYRql4M127PdYzfwvLJJXMkO+CMb8Np4VxqDAg5S8jLdwlEuBis/ia9DKw2M8dFx5u8Mhw==", "license": "MIT", "dependencies": {"@corvu/utils": "~0.4.1"}, "peerDependencies": {"solid-js": "^1.8"}}, "node_modules/starlight-links-validator": {"resolved": "../../node_modules/.pnpm/starlight-links-validator@0.16.0_@astrojs+starlight@0.34.3_astro@5.8.0_@azure+storage-b_cb092014de3e305f7f9a2901f248f83e/node_modules/starlight-links-validator", "link": true}, "node_modules/starlight-theme-rapide": {"resolved": "../../node_modules/.pnpm/starlight-theme-rapide@0.5.1_@astrojs+starlight@0.34.3_astro@5.8.0_@azure+storage-blob@_5f544216a4e1ec7b2f6d8216c5c195ff/node_modules/starlight-theme-rapide", "link": true}, "node_modules/tailwind-merge": {"resolved": "../../node_modules/.pnpm/tailwind-merge@2.6.0/node_modules/tailwind-merge", "link": true}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD"}, "node_modules/typescript": {"resolved": "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript", "link": true}, "node_modules/unocss": {"resolved": "../../node_modules/.pnpm/unocss@0.65.0-beta.2_postcss@8.5.3_rollup@4.39.0_vite@6.3.4_@types+node@22.15.18_jiti@2_21784306d3aa16d487b93eb21b3d7dd5/node_modules/unocss", "link": true}, "node_modules/unocss-preset-animations": {"resolved": "../../node_modules/.pnpm/unocss-preset-animations@1.2.1_unocss@0.65.0-beta.2_postcss@8.5.3_rollup@4.39.0_vite@6._5ff74c8d4816a6009793de99eed33b3b/node_modules/unocss-preset-animations", "link": true}, "node_modules/zod-to-json-schema": {"resolved": "../../node_modules/.pnpm/zod-to-json-schema@3.24.5_zod@3.24.4/node_modules/zod-to-json-schema", "link": true}}}