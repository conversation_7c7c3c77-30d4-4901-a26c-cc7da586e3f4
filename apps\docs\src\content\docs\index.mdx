---
title: Papra documentation
description: Documentation for <PERSON>pra, the minimalistic document archiving platform.
hero:
  title: Papra Docs
  tagline: Documentation for <PERSON>pra, the minimalistic document archiving platform.
  image:
    alt: A glittering, brightly colored logo
    dark: ../../assets/logo-dark.svg
    light: ../../assets/logo-light.svg
  actions:
    - text: Self-hosting guide
      link: /self-hosting/using-docker
      icon: right-arrow
      variant: primary

---

import { LinkCard } from '@astrojs/starlight/components';


Welcome to the official documentation of Papra, an intuitive open-source document management and archiving platform. It is designed to be simple to use and accessible to everyone. Papra is a plateform for long-term document storage and management, like a digital archive for your documents.

Papra can be used in two different ways:

- As a **self-hosted solution**, using the fully packaged lightweight [Docker image](/self-hosting/using-docker).
- As a **fully managed solution**, using our cloud service available on [papra.app](https://papra.app).

<div style="margin-top: 40px">
<LinkCard
title="Get started" 
description="Learn how to self-host <PERSON><PERSON> using Docker."
href="/self-hosting/using-docker"
/>
</div>

## Why <PERSON><PERSON>?

In today's digital world, managing countless important documents efficiently and securely has become crucial. <PERSON><PERSON> helps you effortlessly keep track of everything from personal files to critical business records, providing peace of mind and enhancing productivity through a robust yet user-friendly system.

## Features

- **Document management**: Upload, store, and manage your documents in one place.
- **Organizations**: Create organizations to manage documents with family, friends, or colleagues.
- **Search**: Quickly search for documents with full-text search.
- **Authentication**: User accounts and authentication.
- **Dark Mode**: A dark theme for those late-night document management sessions.
- **Responsive Design**: Works on all devices, from desktops to mobile phones.
- **Open Source**: The project is open-source and free to use.
- **Self-hosting**: Host your own instance of Papra using Docker or other methods.
- **Tags**: Organize your documents with tags.
- **Email ingestion**: Send/forward emails to a generated address to automatically import documents.
- **Content extraction**: Automatically extract text from images or scanned documents for search.
- **Tagging Rules**: Automatically tag documents based on custom rules.
- **Folder ingestion**: Automatically import documents from a folder.
- **API, SDK and webhooks**: Build your own applications on top of Papra.
- **CLI**: Manage your documents from the command line.
- **i18n**: Support for multiple languages.
- *Coming soon:* **Document sharing**: Share documents with others.
- *Coming soon:* **Document requests**: Generate upload links for people to add documents.

## Community & Open Source

Papra is proudly open-source under the [AGPL3](https://github.com/papra-hq/papra/blob/main/LICENSE) license. You can contribute to the project by reporting issues, suggesting features, or even contributing code.



<div style="margin-top: 40px">
<LinkCard
title="Get started" 
description="Learn how to self-host Papra using Docker."
href="/self-hosting/using-docker"
/>
</div>
