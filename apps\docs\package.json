{"name": "@papra/docs", "type": "module", "version": "0.5.1", "private": true, "packageManager": "pnpm@10.12.3", "description": "Papra documentation website", "author": "Corentin <PERSON> <<EMAIL>> (https://corentin.tech)", "license": "AGPL-3.0-or-later", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "lint": "eslint .", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"@astrojs/solid-js": "^5.1.0", "@astrojs/starlight": "^0.34.3", "astro": "^5.8.0", "sharp": "^0.32.5", "shiki": "^3.4.2", "starlight-links-validator": "^0.16.0", "starlight-theme-rapide": "^0.5.0", "tailwind-merge": "^2.6.0", "unocss-preset-animations": "^1.2.1", "yaml": "^2.8.0", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@antfu/eslint-config": "^3.13.0", "@iconify-json/tabler": "^1.1.120", "@types/lodash-es": "^4.17.12", "@unocss/reset": "^0.64.0", "eslint": "^9.17.0", "eslint-plugin-astro": "^1.3.1", "figue": "^2.2.2", "lodash-es": "^4.17.21", "marked": "^15.0.6", "typescript": "^5.7.3", "unocss": "0.65.0-beta.2"}}