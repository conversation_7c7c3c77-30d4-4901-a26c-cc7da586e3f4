{"name": "@papra/app-client", "type": "module", "version": "0.6.3", "private": true, "packageManager": "pnpm@10.12.3", "description": "Papra frontend client", "author": "Corentin <PERSON> <<EMAIL>> (https://corentin.tech)", "license": "AGPL-3.0-or-later", "repository": {"type": "git", "url": "https://github.com/papra-hq/papra"}, "engines": {"node": ">=22.0.0"}, "scripts": {"start": "vite", "dev": "vite", "build": "vite build", "serve": "vite preview", "lint": "eslint .", "lint:fix": "eslint --fix .", "test": "vitest run", "test:watch": "vitest watch", "test:e2e": "playwright test", "typecheck": "tsc --noEmit", "script:get-missing-i18n-keys": "tsx src/scripts/get-missing-i18n-keys.script.ts", "script:generate-i18n-types": "tsx src/scripts/generate-i18n-types.script.ts", "script:sync-i18n-key-order": "tsx src/scripts/sync-i18n-key-order.script.ts"}, "dependencies": {"@corentinth/chisels": "^1.3.1", "@kobalte/core": "^0.13.10", "@kobalte/utils": "^0.9.1", "@modular-forms/solid": "^0.25.1", "@pdfslick/solid": "^2.3.0", "@solid-primitives/storage": "^4.3.2", "@solidjs/router": "^0.14.10", "@tanstack/solid-query": "^5.81.2", "@tanstack/solid-table": "^8.21.3", "@unocss/reset": "^0.64.1", "better-auth": "catalog:", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk-solid": "^1.1.2", "date-fns": "^4.1.0", "lodash-es": "^4.17.21", "ofetch": "^1.4.1", "posthog-js": "^1.255.1", "radix3": "^1.1.2", "solid-js": "^1.9.7", "solid-sonner": "^0.2.8", "tailwind-merge": "^2.6.0", "ts-pattern": "^5.7.1", "unocss-preset-animations": "^1.2.1", "unstorage": "^1.16.0", "valibot": "1.0.0-beta.10"}, "devDependencies": {"@antfu/eslint-config": "catalog:", "@iconify-json/tabler": "^1.2.19", "@playwright/test": "^1.53.1", "@types/lodash-es": "^4.17.12", "@types/node": "catalog:", "eslint": "catalog:", "jsdom": "^25.0.1", "tinyglobby": "^0.2.14", "tsx": "^4.20.3", "typescript": "catalog:", "unocss": "0.65.0-beta.2", "vite": "^5.4.19", "vite-plugin-solid": "^2.11.7", "vitest": "catalog:", "yaml": "^2.8.0"}}