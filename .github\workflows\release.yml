name: Release

on:
  push:
    branches:
      - main

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      id-token: write
      actions: write
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      
      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm i

      - name: Create Release Pull Request
        id: changesets
        uses: changesets/action@v1
        with:
          # Note: pnpm install after versioning is necessary to refresh lockfile
          version: pnpm run version
          publish: pnpm exec changeset publish
          commit: "chore(release): update versions"
          title: "chore(release): update versions"
        env:
          GITHUB_TOKEN: ${{ secrets.CHANGESET_GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: <PERSON><PERSON> Docker build
        if: steps.changesets.outputs.published == 'true' && contains(fromJson(steps.changesets.outputs.publishedPackages).*.name, '@papra/app-server')
        run: |
          VERSION=$(echo '${{ steps.changesets.outputs.publishedPackages }}' | jq -r '.[] | select(.name=="@papra/app-server") | .version')
          echo "VERSION: $VERSION"
          gh workflow run release-docker.yaml -f version="$VERSION"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
