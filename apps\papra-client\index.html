<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <title>Papra - Document archiving and sharing platform</title>

    <meta name="title" content="Papra - Document archiving and sharing platform">
    <meta name="description" content="Papra, the document archiving and sharing platform.">
 
    <link rel="author" href="humans.txt" />
    <link rel="canonical" href="https://papra.app/" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://papra.app/">
    <meta property="og:title" content="Papra - Document archiving and sharing platform">
    <meta property="og:description" content="Papra, the document archiving and sharing platform.">
    <meta property="og:image" content="https://papra.app/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://papra.app/">
    <meta property="twitter:title" content="Papra - Document archiving and sharing platform">
    <meta property="twitter:description" content="Papra, the document archiving and sharing platform.">
    <meta property="twitter:image" content="https://papra.app/og-image.png">

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Structured Data (JSON-LD for rich snippets) -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "Papra",
      "url": "https://papra.app/",
      "description": "Papra, the document archiving and sharing platform.",
      "applicationCategory": "Utilities",
      "operatingSystem": "Web",
      "browserRequirements": "Requires JavaScript",
      "image": "https://papra.app/og-image.png",
      "creator": {
        "@type": "Organization",
        "name": "Papra"
      }
    }
    </script>

    <style>.sr-only {position: absolute;width: 1px;height: 1px;padding: 0;margin: -1px;overflow: hidden;clip: rect(0, 0, 0, 0);white-space: nowrap;border-width: 0;}</style>
  </head>
  <body>
    <h1 class="sr-only">Papra - Document archiving and sharing platform</h1>
    <p class="sr-only">Papra, the document archiving and sharing platform.</p>

    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>

    <script src="/src/index.tsx" type="module"></script>
  </body>
</html>
