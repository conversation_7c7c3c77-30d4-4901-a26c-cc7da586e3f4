name: CI - Lecture

on:
  pull_request:
  push:
    branches:
      - main

jobs:
  ci-packages-lecture:
    name: CI - Lecture
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: packages/lecture

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      
      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm i

      - name: Run linters
        run: pnpm lint

      - name: Type check
        run: pnpm typecheck

      - name: Run unit test
        run: pnpm test

      - name: Build the app
        run: pnpm build