name: CI - App Client

on:
  pull_request:
  push:
    branches:
      - main

jobs:
  ci-apps-papra-client:
    name: CI - Papra Client
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: apps/papra-client

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      
      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm i
        working-directory: ./

      - name: Run linters
        run: pnpm lint

      - name: Type check
        run: pnpm typecheck

      - name: Run unit test
        run: pnpm test

      # Ensure locales types are up to date, must be run before building the app
      - name: Check locales types
        run: |
          pnpm script:generate-i18n-types
          git diff --exit-code -- src/modules/i18n/locales.types.ts > /dev/null || (echo "Locales types are outdated, please run 'pnpm script:generate-i18n-types' and commit the changes." && exit 1)

      - name: Build the app
        run: pnpm build

