{"version": "6", "dialect": "sqlite", "id": "cdc4a655-0dd1-42e5-8e2d-516570b59da6", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"documents": {"name": "documents", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_deleted": {"name": "is_deleted", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "deleted_at": {"name": "deleted_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "deleted_by": {"name": "deleted_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "original_name": {"name": "original_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "original_size": {"name": "original_size", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "original_storage_key": {"name": "original_storage_key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "original_sha256_hash": {"name": "original_sha256_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}}, "indexes": {"documents_organization_id_is_deleted_created_at_index": {"name": "documents_organization_id_is_deleted_created_at_index", "columns": ["organization_id", "is_deleted", "created_at"], "isUnique": false}, "documents_organization_id_is_deleted_index": {"name": "documents_organization_id_is_deleted_index", "columns": ["organization_id", "is_deleted"], "isUnique": false}, "documents_organization_id_original_sha256_hash_unique": {"name": "documents_organization_id_original_sha256_hash_unique", "columns": ["organization_id", "original_sha256_hash"], "isUnique": true}, "documents_original_sha256_hash_index": {"name": "documents_original_sha256_hash_index", "columns": ["original_sha256_hash"], "isUnique": false}, "documents_organization_id_size_index": {"name": "documents_organization_id_size_index", "columns": ["organization_id", "original_size"], "isUnique": false}}, "foreignKeys": {"documents_organization_id_organizations_id_fk": {"name": "documents_organization_id_organizations_id_fk", "tableFrom": "documents", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "documents_created_by_users_id_fk": {"name": "documents_created_by_users_id_fk", "tableFrom": "documents", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "documents_deleted_by_users_id_fk": {"name": "documents_deleted_by_users_id_fk", "tableFrom": "documents", "tableTo": "users", "columnsFrom": ["deleted_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "organization_invitations": {"name": "organization_invitations", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "inviter_id": {"name": "inviter_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"organization_invitations_organization_id_organizations_id_fk": {"name": "organization_invitations_organization_id_organizations_id_fk", "tableFrom": "organization_invitations", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "organization_invitations_inviter_id_users_id_fk": {"name": "organization_invitations_inviter_id_users_id_fk", "tableFrom": "organization_invitations", "tableTo": "users", "columnsFrom": ["inviter_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "organization_members": {"name": "organization_members", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"organization_members_user_organization_unique": {"name": "organization_members_user_organization_unique", "columns": ["organization_id", "user_id"], "isUnique": true}}, "foreignKeys": {"organization_members_organization_id_organizations_id_fk": {"name": "organization_members_organization_id_organizations_id_fk", "tableFrom": "organization_members", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "organization_members_user_id_users_id_fk": {"name": "organization_members_user_id_users_id_fk", "tableFrom": "organization_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "organizations": {"name": "organizations", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_roles": {"name": "user_roles", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"user_roles_role_index": {"name": "user_roles_role_index", "columns": ["role"], "isUnique": false}, "user_roles_user_id_role_unique_index": {"name": "user_roles_user_id_role_unique_index", "columns": ["user_id", "role"], "isUnique": true}}, "foreignKeys": {"user_roles_user_id_users_id_fk": {"name": "user_roles_user_id_users_id_fk", "tableFrom": "user_roles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "documents_tags": {"name": "documents_tags", "columns": {"document_id": {"name": "document_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "tag_id": {"name": "tag_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"documents_tags_document_id_documents_id_fk": {"name": "documents_tags_document_id_documents_id_fk", "tableFrom": "documents_tags", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "documents_tags_tag_id_tags_id_fk": {"name": "documents_tags_tag_id_tags_id_fk", "tableFrom": "documents_tags", "tableTo": "tags", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"documents_tags_pkey": {"columns": ["document_id", "tag_id"], "name": "documents_tags_pkey"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "tags": {"name": "tags", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"tags_organization_id_name_unique": {"name": "tags_organization_id_name_unique", "columns": ["organization_id", "name"], "isUnique": true}}, "foreignKeys": {"tags_organization_id_organizations_id_fk": {"name": "tags_organization_id_organizations_id_fk", "tableFrom": "tags", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email_verified": {"name": "email_verified", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "max_organization_count": {"name": "max_organization_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}, "users_email_index": {"name": "users_email_index", "columns": ["email"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "auth_accounts": {"name": "auth_accounts", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"auth_accounts_user_id_users_id_fk": {"name": "auth_accounts_user_id_users_id_fk", "tableFrom": "auth_accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "auth_sessions": {"name": "auth_sessions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "active_organization_id": {"name": "active_organization_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"auth_sessions_token_index": {"name": "auth_sessions_token_index", "columns": ["token"], "isUnique": false}}, "foreignKeys": {"auth_sessions_user_id_users_id_fk": {"name": "auth_sessions_user_id_users_id_fk", "tableFrom": "auth_sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "auth_sessions_active_organization_id_organizations_id_fk": {"name": "auth_sessions_active_organization_id_organizations_id_fk", "tableFrom": "auth_sessions", "tableTo": "organizations", "columnsFrom": ["active_organization_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "auth_verifications": {"name": "auth_verifications", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"auth_verifications_identifier_index": {"name": "auth_verifications_identifier_index", "columns": ["identifier"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "intake_emails": {"name": "intake_emails", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "email_address": {"name": "email_address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "allowed_origins": {"name": "allowed_origins", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'[]'"}, "is_enabled": {"name": "is_enabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}}, "indexes": {"intake_emails_email_address_unique": {"name": "intake_emails_email_address_unique", "columns": ["email_address"], "isUnique": true}}, "foreignKeys": {"intake_emails_organization_id_organizations_id_fk": {"name": "intake_emails_organization_id_organizations_id_fk", "tableFrom": "intake_emails", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "organization_subscriptions": {"name": "organization_subscriptions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "plan_id": {"name": "plan_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "seats_count": {"name": "seats_count", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "current_period_end": {"name": "current_period_end", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "current_period_start": {"name": "current_period_start", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "cancel_at_period_end": {"name": "cancel_at_period_end", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"organization_subscriptions_organization_id_organizations_id_fk": {"name": "organization_subscriptions_organization_id_organizations_id_fk", "tableFrom": "organization_subscriptions", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}