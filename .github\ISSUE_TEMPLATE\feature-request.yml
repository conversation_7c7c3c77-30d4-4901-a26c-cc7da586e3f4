name: 🚀 New feature proposal
description: Propose a new feature/enhancement
labels: ['enhancement', 'triage']

body:
  - type: markdown
    attributes:
      value: |
        Thanks for your interest in the project and taking the time to fill out this feature report!

  - type: dropdown
    id: request-type
    attributes:
      label: What type of request is this?
      options:
        - New feature idea
        - Enhancement of an existing feature
        - Deployment or CI/CD improvement
        - Hosting or Self-hosting improvement
        - Related to documentation
        - Related to the community
        - Other
    validations:
      required: true

  - type: textarea
    id: feature-description
    attributes:
      label: Clear and concise description of the feature you are proposing
      description: A clear and concise description of what the feature is.
      placeholder: 'Example: I would like to see...'
    validations:
      required: true

  - type: textarea
    id: additional-context
    attributes:
      label: Additional context
      description: Any other context or screenshots about the feature request here.

  - type: checkboxes
    id: checkboxes
    attributes:
      label: Validations
      description: Before submitting the issue, please make sure you do the following
      options:
        - label: Check the feature is not already implemented in the project.
          required: true
        - label: Check that there isn't already an issue that request the same feature to avoid creating a duplicate.
          required: true
        - label: Check that the feature is technically feasible and aligns with the project's goals.
          required: true 