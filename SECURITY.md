# Security Policy

Security is critically important to <PERSON><PERSON>. We actively welcome responsible disclosure of any vulnerabilities found in our platform.

## Reporting a Vulnerability

If you discover a security issue within Papra, please email us directly at **<EMAIL>** with the following details:

- Clear description of the vulnerability.
- Steps or proof-of-concept to reproduce the vulnerability.
- Potential impact or implications of the vulnerability.

We ask you **not to publicly disclose the vulnerability** until we have had a reasonable opportunity to address it.

## Response and Communication

We will:

- Acknowledge receipt of your report within **48 hours**.
- Investigate and provide initial feedback within **5 business days**.
- Work diligently to fix validated vulnerabilities.
- Keep you updated throughout the process until the issue is resolved.

## Security Practices at Papra

Papra follows industry-standard security practices:

- Secure hosting infrastructure provided by trusted services (Render, Cloudflare, Turso).
- Regular security and dependency updates.
- Strict access controls to production environments.
- Encryption of data in transit and at rest.

## Acknowledgments

We greatly appreciate and acknowledge all researchers who responsibly report vulnerabilities, helping us keep Papra secure.
