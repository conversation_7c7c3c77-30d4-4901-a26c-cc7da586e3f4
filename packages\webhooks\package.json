{"name": "@papra/webhooks", "type": "module", "version": "0.1.0", "packageManager": "pnpm@10.12.3", "description": "Webhooks helper library for Papra, the document archiving platform.", "author": "Corentin <PERSON> <<EMAIL>> (https://corentin.tech)", "license": "AGPL-3.0-or-later", "repository": {"type": "git", "url": "https://github.com/papra-hq/papra", "directory": "packages/webhooks"}, "bugs": {"url": "https://github.com/papra-hq/papra/issues"}, "keywords": ["papra", "webhooks", "document", "archiving", "storage"], "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"lint": "eslint .", "lint:fix": "eslint --fix .", "test": "vitest run", "test:watch": "vitest watch", "typecheck": "tsc --noEmit", "build": "unbuild"}, "dependencies": {"@corentinth/chisels": "^1.3.0", "ofetch": "^1.4.1", "tsee": "^1.3.4"}, "devDependencies": {"@antfu/eslint-config": "catalog:", "eslint": "catalog:", "typescript": "catalog:", "unbuild": "^3.5.0", "vitest": "catalog:"}}