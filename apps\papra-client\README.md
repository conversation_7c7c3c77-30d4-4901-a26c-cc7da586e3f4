# Papra - App Client

This is the server for [Papra](https://papra.app).

## Development

> [!IMPORTANT]
> Unless you are developing for the demo mode (`VITE_IS_DEMO_MODE=true` in adjacent `.env` file), you will need to have the server running locally. See the [server README](../papra-server/README.md) for instructions on how to start the server.

To start the development server, run:

```bash
# Navigate to the docs directory
cd apps/papra-client

# Install dependencies
pnpm install

# Start the development server
pnpm dev
```

The development server will be available at [http://localhost:3000](http://localhost:3000).
