name: 🐞 Bug Report
description: File a bug report.
labels: ['bug', 'triage']
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!

  - type: textarea
    id: bug-description
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is. If you intend to submit a PR for this issue, tell us in the description. Thanks!
      placeholder: Bug description
    validations:
      required: true

  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen? If you have a screenshot, you can paste it here.
      placeholder: Tell us what you see!
      value: 'A bug happened!'
    validations:
      required: true

  - type: textarea
    id: version
    attributes:
      label: System information
      description: What is you environment? You can use the `npx envinfo --system --browsers` command to get this information.
    validations:
      required: true

  - type: dropdown
    id: app-type
    attributes:
      label: Where did you encounter the bug?
      options:
        - Demo app (demo.papra.app)
        - Public app (dashboard.papra.app
        - Documentation website (docs.papra.ap)
        - A self hosted instance
        - Other (installations, docker, etc.)
    validations:
      required: true